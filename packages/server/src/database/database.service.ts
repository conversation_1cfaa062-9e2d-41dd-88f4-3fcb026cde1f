import {
  Injectable,
  Logger,
  OnM<PERSON>uleDestroy,
  OnModuleInit,
} from '@nestjs/common'
import { Sequelize } from '@infra-node/sequelize'
import { ModelManager } from './models'
import { IS_CORP } from '@/constants/envs'

function atob(base64String: string): string {
  return Buffer.from(base64String, 'base64').toString('binary')
}

const CORP_DB_ADDRESS = atob(
  '************************************************************************************************************************************************',
)
const STAGING_DB_ADDRESS = atob(
  '********************************************************************************************************************************************************************************',
)

@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DatabaseService.name)
  private sequelize: Sequelize
  private modelManager: ModelManager

  constructor() {
    const address = IS_CORP ? CORP_DB_ADDRESS : STAGING_DB_ADDRESS

    this.sequelize = new Sequelize(address, {
      dialect: 'mysql',
      // serviceName: 'material-platform-metrics-service', // 用于 Grafana 面板筛选
      logging: (sql: string): void => {
        // 在开发环境输出 SQL 日志，生产环境关闭
        if (process.env.NODE_ENV === 'development') {
          this.logger.debug(`SQL: ${sql}`)
        }
      },
    })

    // 初始化模型管理器
    this.modelManager = ModelManager.getInstance(this.sequelize)
    this.modelManager.initModels()

    this.logger.log(`数据库服务初始化完成`)
  }

  async onModuleInit(): Promise<void> {
    try {
      // 测试数据库连接
      await this.sequelize.authenticate()
      this.logger.log('数据库连接成功')
    }
    catch (error) {
      this.logger.error('数据库连接失败', error)
      throw error
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      await this.sequelize.close()
      this.logger.log('数据库连接已关闭')
    }
    catch (error) {
      this.logger.error('关闭数据库连接时出错', error)
    }
  }

  /**
   * 获取 Sequelize 实例
   */
  getSequelize(): Sequelize {
    return this.sequelize
  }

  /**
   * 获取模型管理器
   */
  getModels(): ModelManager {
    return this.modelManager
  }

  /**
   * 执行原生 SQL 查询
   */
  async query(
    sql: string,
    options?: Record<string, unknown>,
  ): Promise<unknown> {
    return this.sequelize.query(sql, options)
  }

  /**
   * 同步数据库模型（谨慎使用，仅在开发环境）
   */
  async sync(options?: Record<string, unknown>): Promise<Sequelize> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('生产环境不允许执行 sync 操作')
    }
    return this.sequelize.sync(options)
  }
}
