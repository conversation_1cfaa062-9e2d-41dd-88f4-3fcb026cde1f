import * as GenUniqueId from 'gen-uniqueid'
import { Sequelize } from '@infra-node/sequelize'
import { MaterialSmartDescriptionJob } from './material-smart-description-job.model'
import { MaterialSmartDescription } from './material-smart-description.model'

const SNOW_FLAKE = new GenUniqueId.SnowFlake({
  workerId: process.env.WorkerId == undefined ? 1 : process.env.WorkerId,
})

export class ModelManager {
  private static instance: ModelManager
  private sequelize: Sequelize
  private _MaterialSmartDescriptionJob?: typeof MaterialSmartDescriptionJob
  private _MaterialSmartDescription?: typeof MaterialSmartDescription

  static genPrimaryIndex(): number {
    return SNOW_FLAKE.NextId()
  }

  private constructor(sequelize: Sequelize) {
    this.sequelize = sequelize
  }

  static getInstance(sequelize: Sequelize): ModelManager {
    if (!ModelManager.instance) {
      ModelManager.instance = new ModelManager(sequelize)
    }
    return ModelManager.instance
  }

  /**
   * 初始化所有模型
   */
  initModels(): void {
    // 初始化所有模型
    this._MaterialSmartDescriptionJob = MaterialSmartDescriptionJob.initModel(this.sequelize)
    this._MaterialSmartDescription = MaterialSmartDescription.initModel(this.sequelize)

    // 这里可以添加模型之间的关联关系
    this.setupAssociations()
  }

  /**
   * 设置模型关联关系
   */
  private setupAssociations(): void {
    // 在这里定义模型之间的关联关系
    // 例如：User.hasMany(Post, { foreignKey: 'userId' });
  }

  /**
   * 获取智能描述生成作业模型
   */
  get MaterialSmartDescriptionJob(): typeof MaterialSmartDescriptionJob {
    if (!this._MaterialSmartDescriptionJob) {
      throw new Error('MaterialSmartDescriptionJob model not initialized. Call initModels() first.')
    }
    return this._MaterialSmartDescriptionJob
  }

  /**
   * 获取智能描述模型
   */
  get MaterialSmartDescription(): typeof MaterialSmartDescription {
    if (!this._MaterialSmartDescription) {
      throw new Error('MaterialSmartDescription model not initialized. Call initModels() first.')
    }
    return this._MaterialSmartDescription
  }
}

// 导出模型类型
export { MaterialSmartDescriptionJob } from './material-smart-description-job.model'
export type {
  MaterialSmartDescriptionJobAttributes,
} from './material-smart-description-job.model'

export { MaterialSmartDescription } from './material-smart-description.model'
export type {
  MaterialSmartDescriptionAttributes,
} from './material-smart-description.model'
