import { DataTypes, Model, Sequelize } from '@infra-node/sequelize'
import { JsonValue } from 'type-fest'

export interface MaterialSmartDescriptionAttributes {
  id: number
  materialId: number
  materialVersion: string
  materialPubId: number
  jobId: number
  smartDescription: JsonValue
  createTime: number
  state: number
  namespace: string
  schemaUrl?: string | null
  publishStatus: number
}

export class MaterialSmartDescription
  extends Model<
    MaterialSmartDescriptionAttributes,
    MaterialSmartDescriptionAttributes
  >
  implements MaterialSmartDescriptionAttributes {
  public id!: number
  public materialId!: number
  public materialVersion!: string
  public materialPubId!: number
  public jobId!: number
  public smartDescription!: JsonValue
  public createTime!: number
  public state!: number
  public namespace!: string
  public schemaUrl?: string | null
  public publishStatus!: number

  /**
   * 初始化智能描述模型
   */
  static initModel(sequelize: Sequelize): typeof MaterialSmartDescription {
    MaterialSmartDescription.init(
      {
        id: {
          type: DataTypes.NUMBER,
          primaryKey: true,
          allowNull: false,
          comment: '唯一标识',
        },
        materialId: {
          type: DataTypes.NUMBER,
          allowNull: false,
          field: 'material_id',
          comment: '物料标识',
        },
        materialVersion: {
          type: DataTypes.STRING(50),
          allowNull: false,
          field: 'material_version',
          comment: '物料版本号',
        },
        materialPubId: {
          type: DataTypes.NUMBER,
          allowNull: false,
          field: 'material_pub_id',
          comment: '冗余字段，直接指向指定物料版本记录',
        },
        jobId: {
          type: DataTypes.NUMBER,
          allowNull: false,
          field: 'job_id',
          comment: '指向过程表',
        },
        smartDescription: {
          type: DataTypes.JSON,
          allowNull: false,
          field: 'smart_description',
          comment: '参考里需要的结构',
        },
        createTime: {
          type: DataTypes.NUMBER,
          allowNull: false,
          field: 'create_time',
          comment: '创建时间（JS 时间戳）',
        },
        state: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 1,
          comment: '生效状态，-1=删除，0=待上线，1=正常',
        },
        namespace: {
          type: DataTypes.STRING(100),
          allowNull: false,
          comment: '命名空间，物料标识的另一种形式',
        },
        schemaUrl: {
          type: DataTypes.STRING(512),
          allowNull: true,
          field: 'schema_url',
          comment: '物料schema详情URL',
        },
        publishStatus: {
          type: DataTypes.TINYINT,
          allowNull: false,
          defaultValue: 0,
          field: 'publish_status',
          comment: '发布状态：0=草稿，1=正式发布',
        },
      },
      {
        sequelize,
        modelName: 'MaterialSmartDescription',
        tableName: 'material_smart_description',
        timestamps: false, // 使用自定义的 create_time 字段
        underscored: false, // 已经手动映射字段名
        comment: '智能描述表',
        indexes: [
          {
            name: 'idx_material_id',
            fields: ['material_id'],
          },
          {
            name: 'idx_material_version',
            fields: ['material_version'],
          },
          {
            name: 'idx_material_pub_id',
            fields: ['material_pub_id'],
          },
          {
            name: 'idx_create_time',
            fields: ['create_time'],
          },
          {
            name: 'idx_job_id',
            fields: ['job_id'],
          },
          {
            name: 'idx_namespace',
            fields: ['namespace'],
          },
          {
            name: 'idx_publish_status',
            fields: ['publish_status'],
          },
        ],
      },
    )

    return MaterialSmartDescription
  }

  /**
   * 检查记录是否有效（未删除）
   */
  isValid(): boolean {
    return this.state >= 0
  }

  /**
   * 检查记录是否已删除
   */
  isDeleted(): boolean {
    return this.state === -1
  }

  /**
   * 检查记录是否待上线
   */
  isPending(): boolean {
    return this.state === 0
  }

  /**
   * 检查记录是否正常
   */
  isActive(): boolean {
    return this.state === 1
  }
}
