import { Module } from '@nestjs/common'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { DatabaseModule } from './database/database.module'
import { MaterialSmartDescriptionJobModule } from './modules/material-smart-description-job/material-smart-description-job.module'
import { MaterialSmartDescriptionModule } from './modules/material-smart-description/material-smart-description.module'

@Module({
  imports: [
    DatabaseModule,
    MaterialSmartDescriptionJobModule,
    MaterialSmartDescriptionModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
