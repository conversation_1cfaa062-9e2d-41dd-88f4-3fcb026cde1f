/**
 * 物料智能描述模块常量定义
 * 统一管理所有魔法数字和配置常量，遵循 KISS 原则
 */

// ==================== 记录状态常量 ====================

/**
 * 记录状态枚举
 */
export const RECORD_STATE = {
  DELETED: -1, // 删除
  PENDING: 0, // 待上线
  ACTIVE: 1, // 正常
} as const

/**
 * 发布状态枚举
 */
export const PUBLISH_STATUS = {
  DRAFT: 0, // 草稿
  PUBLISHED: 1, // 正式发布
} as const

// ==================== 查询限制常量 ====================

/**
 * 分页查询限制
 */
export const PAGINATION_LIMITS = {
  MAX_PAGE_SIZE: 100, // 最大页面大小
  DEFAULT_PAGE_SIZE: 10, // 默认页面大小
  MIN_PAGE_SIZE: 1, // 最小页面大小
  MAX_OFFSET: 10000, // 最大偏移量
} as const

/**
 * 字段长度限制
 */
export const FIELD_LIMITS = {
  MAX_NAMESPACE_LENGTH: 100, // 命名空间最大长度
  MAX_VERSION_LENGTH: 50, // 版本号最大长度
  MAX_DESCRIPTION_LENGTH: 5000, // 描述最大长度
  MAX_SCHEMA_URL_LENGTH: 512, // Schema URL 最大长度
} as const

// ==================== 查询条件常量 ====================

/**
 * 默认查询条件
 */
export const DEFAULT_QUERY_CONDITIONS = {
  STATE: RECORD_STATE.ACTIVE, // 默认查询正常状态的记录
  PUBLISH_STATUS: PUBLISH_STATUS.PUBLISHED, // 默认查询已发布的记录
  ORDER_BY: 'createTime', // 默认排序字段
  ORDER_DIRECTION: 'DESC', // 默认排序方向
} as const

/**
 * 查询操作符
 */
export const QUERY_OPERATORS = {
  LIKE: 'like',
  EQUAL: 'equal',
  IN: 'in',
  BETWEEN: 'between',
  GREATER_THAN: 'gt',
  LESS_THAN: 'lt',
} as const

// ==================== 业务逻辑常量 ====================

/**
 * 版本比较相关常量
 */
export const VERSION_CONSTANTS = {
  SEMVER_PATTERN: /^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?(\+[a-zA-Z0-9.-]+)?$/,
  MAX_MAJOR_VERSION_DIFF: 1, // 版本回退时允许的最大主版本号差异
} as const

/**
 * 命名空间相关常量
 */
export const NAMESPACE_CONSTANTS = {
  PATTERN: /^[a-zA-Z0-9._-]+$/, // 命名空间格式验证正则
  SEPARATOR: '::', // 命名空间分隔符
  DEFAULT_PREFIX: '@es', // 默认命名空间前缀
} as const

/**
 * Schema 相关常量
 */
export const SCHEMA_CONSTANTS = {
  REQUIRED_FIELDS: ['componentName'], // Schema 必需字段
  CDN_TIMEOUT: 5000, // CDN 请求超时时间（毫秒）
  CACHE_TTL: 300, // Schema 缓存时间（秒）
} as const

// ==================== 错误消息常量 ====================

/**
 * 通用错误消息
 */
export const ERROR_MESSAGES = {
  RECORD_NOT_FOUND: '记录不存在',
  INVALID_PARAMETERS: '参数无效',
  VALIDATION_FAILED: '验证失败',
  OPERATION_FAILED: '操作失败',
  PERMISSION_DENIED: '权限不足',
  NETWORK_ERROR: '网络错误',
  TIMEOUT_ERROR: '请求超时',
  UNKNOWN_ERROR: '未知错误',
} as const

/**
 * 特定业务错误消息
 */
export const BUSINESS_ERROR_MESSAGES = {
  MATERIAL_NOT_FOUND: '物料不存在',
  VERSION_NOT_FOUND: '版本不存在',
  DESCRIPTION_NOT_FOUND: '智能描述不存在',
  SCHEMA_INVALID: 'Schema 格式无效',
  VERSION_FORMAT_INVALID: '版本号格式无效',
  NAMESPACE_FORMAT_INVALID: '命名空间格式无效',
  FALLBACK_NOT_AVAILABLE: '没有可用的回退版本',
  COMPATIBILITY_CHECK_FAILED: '兼容性检查失败',
} as const

// ==================== 日志相关常量 ====================

/**
 * 日志级别
 */
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
} as const

/**
 * 日志消息模板
 */
export const LOG_MESSAGES = {
  QUERY_SUCCESS: '查询成功',
  QUERY_FAILED: '查询失败',
  CREATE_SUCCESS: '创建成功',
  CREATE_FAILED: '创建失败',
  UPDATE_SUCCESS: '更新成功',
  UPDATE_FAILED: '更新失败',
  DELETE_SUCCESS: '删除成功',
  DELETE_FAILED: '删除失败',
  VALIDATION_SUCCESS: '验证成功',
  VALIDATION_FAILED: '验证失败',
  FALLBACK_TRIGGERED: '触发版本回退',
  FALLBACK_SUCCESS: '版本回退成功',
  FALLBACK_FAILED: '版本回退失败',
} as const

// ==================== 性能相关常量 ====================

/**
 * 缓存相关常量
 */
export const CACHE_CONSTANTS = {
  DEFAULT_TTL: 300, // 默认缓存时间（秒）
  SCHEMA_CACHE_TTL: 600, // Schema 缓存时间（秒）
  QUERY_CACHE_TTL: 60, // 查询结果缓存时间（秒）
  MAX_CACHE_SIZE: 1000, // 最大缓存条目数
} as const

/**
 * 批量操作相关常量
 */
export const BATCH_CONSTANTS = {
  MAX_BATCH_SIZE: 100, // 最大批量操作数量
  DEFAULT_BATCH_SIZE: 10, // 默认批量操作数量
  BATCH_TIMEOUT: 30000, // 批量操作超时时间（毫秒）
} as const

// ==================== 类型守卫辅助函数 ====================

/**
 * 检查是否为有效的记录状态
 */
export function isValidRecordState(state: unknown): state is number {
  return (
    typeof state === 'number'
    && (Object.values(RECORD_STATE) as number[]).includes(state)
  )
}

/**
 * 检查是否为有效的发布状态
 */
export function isValidPublishStatus(status: unknown): status is number {
  return (
    typeof status === 'number'
    && (Object.values(PUBLISH_STATUS) as number[]).includes(status)
  )
}

/**
 * 检查是否为有效的命名空间格式
 */
export function isValidNamespaceFormat(
  namespace: unknown,
): namespace is string {
  return (
    typeof namespace === 'string'
    && namespace.length <= FIELD_LIMITS.MAX_NAMESPACE_LENGTH
    && NAMESPACE_CONSTANTS.PATTERN.test(namespace)
  )
}

/**
 * 检查是否为有效的版本号格式
 */
export function isValidVersionFormat(version: unknown): version is string {
  return (
    typeof version === 'string'
    && version.length <= FIELD_LIMITS.MAX_VERSION_LENGTH
    && VERSION_CONSTANTS.SEMVER_PATTERN.test(version)
  )
}

/**
 * 检查分页参数是否有效
 */
export function isValidPaginationParams(params: {
  pageNum?: unknown
  pageSize?: unknown
}): params is { pageNum: number, pageSize: number } {
  const { pageNum, pageSize } = params

  return (
    typeof pageNum === 'number'
    && typeof pageSize === 'number'
    && pageNum >= 1
    && pageSize >= PAGINATION_LIMITS.MIN_PAGE_SIZE
    && pageSize <= PAGINATION_LIMITS.MAX_PAGE_SIZE
  )
}

// ==================== 导出所有常量 ====================

/**
 * 统一导出所有常量
 */
export const MATERIAL_SMART_DESCRIPTION_CONSTANTS = {
  RECORD_STATE,
  PUBLISH_STATUS,
  PAGINATION_LIMITS,
  FIELD_LIMITS,
  DEFAULT_QUERY_CONDITIONS,
  QUERY_OPERATORS,
  VERSION_CONSTANTS,
  NAMESPACE_CONSTANTS,
  SCHEMA_CONSTANTS,
  ERROR_MESSAGES,
  BUSINESS_ERROR_MESSAGES,
  LOG_LEVELS,
  LOG_MESSAGES,
  CACHE_CONSTANTS,
  BATCH_CONSTANTS,
} as const

/**
 * 导出类型守卫函数
 */
export const VALIDATION_HELPERS = {
  isValidRecordState,
  isValidPublishStatus,
  isValidNamespaceFormat,
  isValidVersionFormat,
  isValidPaginationParams,
} as const
