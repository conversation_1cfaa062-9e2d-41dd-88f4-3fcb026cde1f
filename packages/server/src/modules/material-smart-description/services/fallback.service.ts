import { Injectable, Logger } from '@nestjs/common'
import * as semver from 'semver'

import { MaterialSmartDescription } from '../../../database/models'
import { MaterialSmartDescriptionQueryService } from './query.service'

/**
 * 物料智能描述版本回退服务
 * 专门负责版本回退和fallback逻辑，遵循单一职责原则
 */
@Injectable()
export class MaterialSmartDescriptionFallbackService {
  private readonly logger = new Logger(
    MaterialSmartDescriptionFallbackService.name,
  )

  constructor(
    private readonly queryService: MaterialSmartDescriptionQueryService,
  ) {}

  /**
   * 通过物料标识参数查询物料的 zhidaDescription，支持版本回退
   * 如果目标版本没有有效的智能描述，会查找版本更小的有效描述
   */
  async getDescriptionWithFallback(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
    buildZhidaDescriptionFn: (data: {
      smartDescription: unknown
      materialDetail: Service.MaterialSmartDescription.MaterialDetailInfo
    }) => Service.MaterialSmartDescription.ZhiDaNeededMaterialDescription,
    getMaterialDetailInfoFromDescriptionFn: (
      description: MaterialSmartDescription
    ) => Promise<Service.MaterialSmartDescription.MaterialDetailInfo>,
  ): Promise<Service.MaterialSmartDescription.ZhiDaNeededMaterialDescriptionWithVersion | null> {
    const targetVersion = params.materialVersionName!

    // 首先尝试查找目标版本的有效且已发布的描述
    const targetDescription
      = await this.queryService.findLatestPublishedDescriptionByMaterialIdentifier(
        params,
      )

    if (targetDescription) {
      // 找到目标版本的描述，直接返回
      const materialDetail = await getMaterialDetailInfoFromDescriptionFn(
        targetDescription,
      )
      const zhidaDescription = buildZhidaDescriptionFn({
        smartDescription: targetDescription.smartDescription,
        materialDetail,
      })

      return {
        content: zhidaDescription,
        isTargetVersion: true,
        actualVersion: targetVersion,
        targetVersion,
      }
    }

    // 目标版本没有有效描述，需要查找同一物料的其他版本
    const materialId = await this.getMaterialIdForFallback(params)

    if (!materialId) {
      return null
    }

    // 获取该物料的所有有效且已发布的描述
    const allDescriptions
      = await this.queryService.findAllPublishedDescriptionsByMaterialId(
        materialId,
      )

    if (allDescriptions.length === 0) {
      return null
    }

    // 查找最佳的回退版本
    const fallbackDescription = this.findBestFallbackVersion(
      allDescriptions,
      targetVersion,
    )

    if (!fallbackDescription) {
      return null
    }

    // 构建回退版本的响应
    const materialDetail = await getMaterialDetailInfoFromDescriptionFn(
      fallbackDescription,
    )
    const zhidaDescription = buildZhidaDescriptionFn({
      smartDescription: fallbackDescription.smartDescription,
      materialDetail,
    })

    return {
      content: zhidaDescription,
      isTargetVersion: false,
      actualVersion: fallbackDescription.materialVersion,
      targetVersion,
    }
  }

  /**
   * 获取用于回退查找的 materialId
   */
  private async getMaterialIdForFallback(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<number | null> {
    let materialId: number | undefined = params.materialId

    if (!materialId) {
      // 如果没有提供 materialId，尝试通过其他方式获取
      // 可以通过 namespace 查找任意一个记录来获取 materialId
      const existingDescription
        = await this.queryService.findLatestDescriptionByMaterialIdentifier({
          namespace: params.namespace,
        })

      if (!existingDescription) {
        return null
      }

      materialId = existingDescription.materialId
    }

    return materialId
  }

  /**
   * 从所有描述中找到最佳的回退版本
   * 选择版本小于目标版本的最新版本
   */
  private findBestFallbackVersion(
    allDescriptions: MaterialSmartDescription[],
    targetVersion: string,
  ): MaterialSmartDescription | null {
    // 过滤出版本小于目标版本的描述，并按版本号排序
    const validDescriptions = allDescriptions
      .filter((desc) => {
        try {
          return semver.lt(desc.materialVersion, targetVersion)
        }
        catch (error) {
          // 如果版本号格式不正确，跳过该记录
          this.logger.warn(`版本号格式错误: ${desc.materialVersion}`, error)
          return false
        }
      })
      .sort((a, b) => {
        try {
          return semver.rcompare(a.materialVersion, b.materialVersion) // 降序排列，最新的在前
        }
        catch (_) {
          // 如果比较失败，按创建时间排序
          return b.createTime - a.createTime
        }
      })

    if (validDescriptions.length === 0) {
      return null
    }

    // 使用最新的有效版本
    return validDescriptions[0]
  }

  /**
   * 检查是否需要进行版本回退
   * 如果目标版本存在有效描述，返回 false；否则返回 true
   */
  async shouldUseFallback(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<boolean> {
    const targetDescription
      = await this.queryService.findLatestPublishedDescriptionByMaterialIdentifier(
        params,
      )
    return !targetDescription
  }

  /**
   * 获取可用的回退版本列表
   * 返回所有版本小于目标版本的有效描述，按版本号降序排列
   */
  async getAvailableFallbackVersions(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<
      Array<{
        version: string
        createTime: number
        description: MaterialSmartDescription
      }>
    > {
    const targetVersion = params.materialVersionName!
    const materialId = await this.getMaterialIdForFallback(params)

    if (!materialId) {
      return []
    }

    const allDescriptions
      = await this.queryService.findAllPublishedDescriptionsByMaterialId(
        materialId,
      )

    return allDescriptions
      .filter((desc) => {
        try {
          return semver.lt(desc.materialVersion, targetVersion)
        }
        catch (error) {
          this.logger.warn(`版本号格式错误: ${desc.materialVersion}`, error)
          return false
        }
      })
      .sort((a, b) => {
        try {
          return semver.rcompare(a.materialVersion, b.materialVersion)
        }
        catch (_) {
          return b.createTime - a.createTime
        }
      })
      .map(desc => ({
        version: desc.materialVersion,
        createTime: desc.createTime,
        description: desc,
      }))
  }

  /**
   * 验证版本回退的合理性
   * 检查目标版本和回退版本之间的兼容性
   */
  validateFallbackCompatibility(
    targetVersion: string,
    fallbackVersion: string,
  ): { isCompatible: boolean, reason?: string } {
    try {
      // 检查版本格式
      if (!semver.valid(targetVersion)) {
        return { isCompatible: false, reason: '目标版本格式无效' }
      }

      if (!semver.valid(fallbackVersion)) {
        return { isCompatible: false, reason: '回退版本格式无效' }
      }

      // 确保回退版本确实小于目标版本
      if (!semver.lt(fallbackVersion, targetVersion)) {
        return { isCompatible: false, reason: '回退版本必须小于目标版本' }
      }

      // 检查主版本号差异（可选的兼容性检查）
      const targetMajor = semver.major(targetVersion)
      const fallbackMajor = semver.major(fallbackVersion)

      if (targetMajor - fallbackMajor > 1) {
        return {
          isCompatible: true, // 仍然兼容，但给出警告
          reason: '主版本号差异较大，可能存在兼容性问题',
        }
      }

      return { isCompatible: true }
    }
    catch (error) {
      return {
        isCompatible: false,
        reason: `版本比较失败: ${
          error instanceof Error ? error.message : '未知错误'
        }`,
      }
    }
  }

  /**
   * 获取版本回退的统计信息
   */
  async getFallbackStatistics(materialId: number): Promise<{
    totalVersions: number
    publishedVersions: number
    latestVersion: string | null
    oldestVersion: string | null
  }> {
    try {
      const allDescriptions
        = await this.queryService.findDescriptionsByMaterialId(materialId)
      const publishedDescriptions
        = await this.queryService.findAllPublishedDescriptionsByMaterialId(
          materialId,
        )

      const versions = publishedDescriptions
        .map(desc => desc.materialVersion)
        .filter(version => semver.valid(version))
        .sort(semver.compare)

      return {
        totalVersions: allDescriptions.length,
        publishedVersions: publishedDescriptions.length,
        latestVersion:
          versions.length > 0 ? versions[versions.length - 1] : null,
        oldestVersion: versions.length > 0 ? versions[0] : null,
      }
    }
    catch (error) {
      this.logger.error(
        `获取回退统计信息失败 materialId: ${materialId}`,
        error,
      )
      return {
        totalVersions: 0,
        publishedVersions: 0,
        latestVersion: null,
        oldestVersion: null,
      }
    }
  }
}
