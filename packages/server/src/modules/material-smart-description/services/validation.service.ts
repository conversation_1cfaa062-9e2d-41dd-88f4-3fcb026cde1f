import { Injectable, Logger } from '@nestjs/common'
import { get } from 'lodash'
import { fetchFromCDN } from '../../../tools/cdn'

// 物料 Schema 接口定义
interface MaterialSchema {
  componentName: string
  componentChineseName?: string
  [key: string]: unknown
}

/**
 * 物料智能描述验证服务
 * 专门负责验证和校验操作，遵循单一职责原则
 */
@Injectable()
export class MaterialSmartDescriptionValidationService {
  private readonly logger = new Logger(
    MaterialSmartDescriptionValidationService.name,
  )

  /**
   * 验证物料标识参数的完整性
   */
  validateMaterialIdentifierParams(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): { isValid: boolean, errors: string[] } {
    const errors: string[] = []

    // 至少需要提供一种标识方式
    if (
      !params.materialId
      && !params.namespace
      && !params.materialVersionName
    ) {
      errors.push(
        '必须提供 materialId、namespace 或 materialVersionName 中的至少一个',
      )
    }

    // 如果提供了 materialVersionName，通常需要配合其他参数
    if (params.materialVersionName && !params.materialId && !params.namespace) {
      errors.push(
        '提供 materialVersionName 时，建议同时提供 materialId 或 namespace',
      )
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * 验证智能描述数据结构的完整性
   */
  validateSmartDescriptionStructure(smartDescription: unknown): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!smartDescription || typeof smartDescription !== 'object') {
      errors.push('智能描述数据必须是一个对象')
      return { isValid: false, errors }
    }

    // 检查必要的字段结构
    const payload = get(smartDescription, 'payload')
    if (!payload || typeof payload !== 'object') {
      errors.push('智能描述必须包含 payload 字段')
    }
    else {
      // 检查 payload 中的关键字段
      const requiredFields = ['description', 'propsDefine']
      for (const field of requiredFields) {
        if (!get(payload, field)) {
          errors.push(`payload 中缺少必要字段: ${field}`)
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * 验证更新参数的有效性
   */
  validateUpdateParams(
    updateDto: Service.MaterialSmartDescription.UpdateZhidaDescriptionDto,
  ): { isValid: boolean, errors: string[] } {
    const errors: string[] = []

    // 检查是否至少提供了一个要更新的字段
    const updateFields = [
      'description',
      'propsDefine',
      'jsxDemo',
      'childNested',
      'jsxPropCompatible',
      'mergePropsBeforeInsert',
      'purePropEffect',
    ]

    const hasUpdateField = updateFields.some(
      field => updateDto[field as keyof typeof updateDto] !== undefined,
    )

    if (!hasUpdateField) {
      errors.push('必须提供至少一个要更新的字段')
    }

    // 验证 JSON 字段格式
    if (updateDto.jsxPropCompatible !== undefined) {
      try {
        if (typeof updateDto.jsxPropCompatible === 'string') {
          JSON.parse(updateDto.jsxPropCompatible)
        }
      }
      catch {
        errors.push('jsxPropCompatible 必须是有效的 JSON 格式')
      }
    }

    if (updateDto.mergePropsBeforeInsert !== undefined) {
      try {
        if (typeof updateDto.mergePropsBeforeInsert === 'string') {
          JSON.parse(updateDto.mergePropsBeforeInsert)
        }
      }
      catch {
        errors.push('mergePropsBeforeInsert 必须是有效的 JSON 格式')
      }
    }

    // 验证数组字段
    if (updateDto.jsxDemo !== undefined && !Array.isArray(updateDto.jsxDemo)) {
      errors.push('jsxDemo 必须是数组格式')
    }

    if (
      updateDto.childNested !== undefined
      && !Array.isArray(updateDto.childNested)
    ) {
      errors.push('childNested 必须是数组格式')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * 验证分页参数
   */
  validatePaginationParams(params: Service.PaginationParams): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!params.pageNum || params.pageNum < 1) {
      errors.push('pageNum 必须是大于 0 的整数')
    }

    if (!params.pageSize || params.pageSize < 1) {
      errors.push('pageSize 必须是大于 0 的整数')
    }

    if (params.pageSize > 100) {
      errors.push('pageSize 不能超过 100')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * 从 CDN 获取并验证 Schema
   */
  async getAndValidateSchemaFromCDN(
    schemaUrl: string,
  ): Promise<MaterialSchema | null> {
    try {
      const schema = await fetchFromCDN(schemaUrl)

      if (!schema) {
        this.logger.warn(`从 CDN 获取 schema 失败: ${schemaUrl}`)
        return null
      }

      // 验证 schema 结构
      if (!this.isValidMaterialSchema(schema)) {
        this.logger.warn(`Schema 结构无效: ${schemaUrl}`)
        return null
      }

      return schema as MaterialSchema
    }
    catch (error) {
      this.logger.error(`获取和验证 schema 失败: ${schemaUrl}`, error)
      return null
    }
  }

  /**
   * 验证物料 Schema 结构是否有效
   */
  private isValidMaterialSchema(schema: unknown): boolean {
    if (!schema || typeof schema !== 'object') {
      return false
    }

    const schemaObj = schema as Record<string, unknown>

    // 检查必要字段
    if (
      !schemaObj.componentName
      || typeof schemaObj.componentName !== 'string'
    ) {
      return false
    }

    return true
  }

  /**
   * 验证命名空间格式
   */
  validateNamespaceFormat(namespace: string): {
    isValid: boolean
    error?: string
  } {
    if (!namespace || typeof namespace !== 'string') {
      return { isValid: false, error: 'namespace 必须是非空字符串' }
    }

    // 简单的命名空间格式验证（可根据实际需求调整）
    const namespacePattern = /^[a-zA-Z0-9._-]+$/
    if (!namespacePattern.test(namespace)) {
      return {
        isValid: false,
        error: 'namespace 只能包含字母、数字、点号、下划线和连字符',
      }
    }

    if (namespace.length > 100) {
      return { isValid: false, error: 'namespace 长度不能超过 100 个字符' }
    }

    return { isValid: true }
  }

  /**
   * 验证版本号格式（semver）
   */
  validateVersionFormat(version: string): { isValid: boolean, error?: string } {
    if (!version || typeof version !== 'string') {
      return { isValid: false, error: '版本号必须是非空字符串' }
    }

    // 简单的 semver 格式验证
    const semverPattern
      = /^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?(\+[a-zA-Z0-9.-]+)?$/
    if (!semverPattern.test(version)) {
      return {
        isValid: false,
        error: '版本号必须符合 semver 格式 (如: 1.0.0, 1.0.0-alpha.1)',
      }
    }

    return { isValid: true }
  }

  /**
   * 验证发布状态值
   */
  validatePublishStatus(publishStatus: number): {
    isValid: boolean
    error?: string
  } {
    const validStatuses = [0, 1] // 0=草稿，1=正式发布

    if (!validStatuses.includes(publishStatus)) {
      return {
        isValid: false,
        error: '发布状态只能是 0（草稿）或 1（正式发布）',
      }
    }

    return { isValid: true }
  }

  /**
   * 验证记录状态值
   */
  validateRecordState(state: number): { isValid: boolean, error?: string } {
    const validStates = [-1, 0, 1] // -1=删除，0=待上线，1=正常

    if (!validStates.includes(state)) {
      return {
        isValid: false,
        error: '记录状态只能是 -1（删除）、0（待上线）或 1（正常）',
      }
    }

    return { isValid: true }
  }
}
