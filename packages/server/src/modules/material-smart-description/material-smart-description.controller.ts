import { Controller, Get, Param, Query, Put, Body, Post } from '@nestjs/common'
import { MaterialSmartDescriptionService } from './material-smart-description.service'
import { MaterialSmartDescription } from '@/database/models'

@Controller('material-smart-description')
export class MaterialSmartDescriptionController {
  constructor(
    private readonly descriptionService: MaterialSmartDescriptionService,
  ) {}

  /**
   * 分页获取按物料分组的智能描述列表
   */
  @Get()
  async getDescriptions(
    @Query() query: Service.PaginationParams,
  ): Promise<Service.MaterialSmartDescription.MaterialGroupedDescriptionList> {
    return this.descriptionService.getGroupedDescriptions(query)
  }

  /**
   * 更新 ZhiDa 描述内容
   * 只允许修改映射到 zhidaDescription 的字段，除了 name 和 namespace
   */
  @Put(':id/zhida-description')
  async updateZhidaDescription(
    @Param('id') id: string,
    @Body()
    updateDto: Service.MaterialSmartDescription.UpdateZhidaDescriptionDto,
  ): Promise<Service.MaterialSmartDescription.EnrichedDescription> {
    const result
      = await this.descriptionService.updateZhidaDescriptionWithEnrichment({
        id: Number(id),
        updateDto,
      })

    if (!result.success) {
      throw new Error(result.error || '更新失败')
    }

    return result.data!
  }

  /**
   * 批量更新 ZhiDa 描述内容
   * 通过 namespace 和 version 定位记录进行批量更新
   */
  @Post('zhida-description/batch')
  async batchUpdateZhidaDescription(
    @Body() request: Service.MaterialSmartDescription.BatchUpdateZhidaDescriptionRequest,
  ): Promise<Service.MaterialSmartDescription.BatchUpdateZhidaDescriptionResponse> {
    return this.descriptionService.batchUpdateZhidaDescription(request.items)
  }

  /**
   * 根据 ID 获取单个描述详情
   */
  @Get(':id')
  async getDescriptionById(
    @Param('id') id: string,
  ): Promise<Service.MaterialSmartDescription.EnrichedDescription> {
    const description
      = await this.descriptionService.getEnrichedDescriptionById(Number(id))

    if (!description) {
      throw new Error('描述不存在')
    }

    return description
  }

  /**
   * 根据物料 ID 获取历史版本
   */
  @Get('material/:materialId/history')
  async getHistoryByMaterialId(
    @Param('materialId') materialId: string,
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<MaterialSmartDescription>> {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize

    const descriptions
      = await this.descriptionService.findDescriptionsByMaterialId(
        Number(materialId),
        pageSize,
        offset,
      )

    // 获取总数
    const allDescriptions
      = await this.descriptionService.findDescriptionsByMaterialId(
        Number(materialId),
        1000,
        0,
      )
    const total = allDescriptions.length
    const totalPage = Math.ceil(total / pageSize)

    return {
      list: descriptions,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 根据 namespace 获取描述列表（支持模糊查询）
   */
  @Get('namespace/:namespace')
  async getDescriptionsByNamespace(
    @Param('namespace') namespace: string,
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<MaterialSmartDescription>> {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize

    // 并行获取数据和总数
    const [descriptions, total] = await Promise.all([
      this.descriptionService.findDescriptionsByNamespace(
        namespace,
        pageSize,
        offset,
      ),
      this.descriptionService.countDescriptionsByNamespace(namespace),
    ])

    const totalPage = Math.ceil(total / pageSize)

    return {
      list: descriptions,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 根据发布状态获取描述列表
   */
  @Get('publish-status/:status')
  async getDescriptionsByPublishStatus(
    @Param('status') status: string,
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<MaterialSmartDescription>> {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize
    const publishStatus = Number(status)

    // 并行获取数据和总数
    const [descriptions, total] = await Promise.all([
      this.descriptionService.findDescriptionsByPublishStatus(
        publishStatus,
        pageSize,
        offset,
      ),
      this.descriptionService.countDescriptionsByPublishStatus(publishStatus),
    ])

    const totalPage = Math.ceil(total / pageSize)

    return {
      list: descriptions,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 更新发布状态
   */
  @Put(':id/publish-status')
  async updatePublishStatus(
    @Param('id') id: string,
    @Body() body: { publishStatus: number },
  ): Promise<{ success: boolean, message: string }> {
    const result = await this.descriptionService.updatePublishStatus(
      Number(id),
      body.publishStatus,
    )

    if (!result.success) {
      throw new Error(result.message)
    }

    return result
  }

  /**
   * 通过物料标识参数查询物料的 zhidaDescription
   */
  @Post('query')
  async getDescriptionByMaterialIdentifier(
    @Body() params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<Service.MaterialSmartDescription.ZhiDaNeededMaterialDescription | null> {
    return this.descriptionService.getDescriptionByMaterialIdentifier(params)
  }

  /**
   * 通过物料标识参数查询物料的 zhidaDescription，支持版本回退
   * 如果目标版本没有有效的智能描述，会查找版本更小的有效描述
   */
  @Post('query-with-fallback')
  async getDescriptionByMaterialIdentifierWithFallback(
    @Body() params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<Service.MaterialSmartDescription.ZhiDaNeededMaterialDescriptionWithVersion | null> {
    return this.descriptionService.getDescriptionByMaterialIdentifierWithFallback(
      params,
    )
  }
}
