import { Module } from '@nestjs/common'
import { DatabaseModule } from '../../database/database.module'
import { MaterialSmartDescriptionService } from './material-smart-description.service'
import { MaterialSmartDescriptionController } from './material-smart-description.controller'
import { ForwardMaterialPlatformModule } from '../forward/material-platform/material-platform.module'

// 导入子服务
import { MaterialSmartDescriptionQueryService } from './services/query.service'
import { MaterialSmartDescriptionCreationService } from './services/creation.service'
import { MaterialSmartDescriptionValidationService } from './services/validation.service'
import { MaterialSmartDescriptionFallbackService } from './services/fallback.service'

@Module({
  imports: [DatabaseModule, ForwardMaterialPlatformModule],
  controllers: [MaterialSmartDescriptionController],
  providers: [
    MaterialSmartDescriptionService,
    MaterialSmartDescriptionQueryService,
    MaterialSmartDescriptionCreationService,
    MaterialSmartDescriptionValidationService,
    MaterialSmartDescriptionFallbackService,
  ],
  exports: [MaterialSmartDescriptionService],
})
export class MaterialSmartDescriptionModule {}
