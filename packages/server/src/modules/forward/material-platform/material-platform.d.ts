declare namespace Service.Forward.MaterialPlatform {
  export interface MaterialIdentifierParams {
    materialId?: number
    namespace?: string
    materialVersionName?: string
  }

  export interface MaterialPlatformMaterialDetail {
    id: number
    namespace: string
    version: string
    business: string
    type: string
    description: string
    gitUrl: string
    tags: Record<string, unknown>
    title: string
    currentVersion: {
      id: number
      material_id: number
      version: string
      readme: string
      preview_img: string
      schema: JsonValue
      content: JsonValue
      creator: string
      creator_name: string
      create_time: number
    }
    creator: string
    creator_name: string
    create_time: number
    updater: string
    updater_name: string
    update_time: string
  }
}
