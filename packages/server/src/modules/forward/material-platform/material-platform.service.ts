import { IS_CORP } from '@/constants/envs'
import { Injectable } from '@nestjs/common'
import axios from 'axios'

@Injectable()
export class ForwardMaterialPlatformService {
  constructor() {}

  static assertMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): void | never {
    const { materialId, namespace, materialVersionName } = params
    if (!materialId && !namespace) {
      throw new Error('必须传入物料 ID 或命名空间')
    }
    if (!materialVersionName) {
      throw new Error('必须传入物料版本名称')
    }
  }

  static get MaterialPlatformDomain(): string {
    if (IS_CORP) {
      return `http://components.internal`
    }
    else {
      return `https://components.corp.kuaishou.com`
    }
  }

  async getMaterialVersionDetail(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<Service.Forward.MaterialPlatform.MaterialPlatformMaterialDetail> {
    ForwardMaterialPlatformService.assertMaterialIdentifier(params)
    return axios
      .post(
        `${ForwardMaterialPlatformService.MaterialPlatformDomain}/api/v2/open/material/detail`,
        {
          id: params.materialId,
          namespace: params.namespace,
          version: params.materialVersionName,
        },
      )
      .then((res) => {
        return res.data.data
      })
      .catch((e) => {
        throw new Error(
          `从物料中台获取物料版本详情失败（${
            params.materialId || params.namespace
          }，${params.materialVersionName}）: ${JSON.stringify(
            e.response.data.message,
          )}`,
        )
      })
  }
}
