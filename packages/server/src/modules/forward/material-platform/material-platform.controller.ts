import { Controller, Get, Query } from '@nestjs/common'
import { ForwardMaterialPlatformService } from './material-platform.service'

@Controller('forward/material-platform')
export class ForwardMaterialPlatformController {
  constructor(
    private readonly forwardMaterialPlatformService: ForwardMaterialPlatformService,
  ) {}

  @Get('detail')
  async materialPlatformDetail(
    @Query() queries: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<Service.Forward.MaterialPlatform.MaterialPlatformMaterialDetail> {
    return this.forwardMaterialPlatformService.getMaterialVersionDetail({
      materialId: queries.materialId ? +queries.materialId : undefined,
      namespace: queries.namespace,
      materialVersionName: queries.materialVersionName,
    })
  }
}
