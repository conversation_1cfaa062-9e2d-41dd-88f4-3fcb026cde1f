import { Module } from '@nestjs/common'
import { MaterialSmartDescriptionJobController } from './material-smart-description-job.controller'
import { MaterialSmartDescriptionJobService } from './material-smart-description-job.service'
import { LLMReadmeGeneratorAgentService } from './llm-readme-generator-agnet.service'
import { ForwardMaterialPlatformModule } from '../forward/material-platform/material-platform.module'
import { MaterialSmartDescriptionModule } from '../material-smart-description/material-smart-description.module'

@Module({
  controllers: [MaterialSmartDescriptionJobController],
  providers: [MaterialSmartDescriptionJobService, LLMReadmeGeneratorAgentService],
  exports: [MaterialSmartDescriptionJobService, LLMReadmeGeneratorAgentService],
  imports: [ForwardMaterialPlatformModule, MaterialSmartDescriptionModule],
})
export class MaterialSmartDescriptionJobModule {}
