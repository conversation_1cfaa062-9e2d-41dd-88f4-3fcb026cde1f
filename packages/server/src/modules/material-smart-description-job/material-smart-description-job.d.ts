declare namespace Service.MaterialSmartDescription.Job {

  export type MessageRole = 'user' | 'assistant'

  /** 单条消息 */
  export interface Message {
    /** 消息角色 */
    role: MessageRole
    /** 文本内容 */
    content: string
    /** 图片 URL（可选） */
    imageUrl?: string
  }

  /** 调用模型服务的请求参数 */
  export interface CallModelParams {
    /** 消息列表 */
    messages: Message[]
    /** 调用参数 */
    options: CallModelOptions
  }

  /** 重试配置 */
  export interface RetryConfig {
    /** 最大重试次数，默认为 3 */
    maxRetries?: number
    /** 重试间隔（毫秒），默认为 1000 */
    retryDelay?: number
    /** 退避系数，每次重试间隔乘以该系数，默认为 2 */
    backoffFactor?: number
    /** 最大重试间隔（毫秒），默认为 10000 */
    maxRetryDelay?: number
  }

  export interface CallModelOptions {
    /** 业务标识 */
    bizKey: string
    /** 模型名称 */
    model?: string
    /** 操作者（内部域名必传） */
    operator?: string
    /** 重试配置 */
    retry?: RetryConfig
  }

  /** LLM 模型服务响应结果 */
  export interface CallModelResult {
    result: number
    code: string
    error_msg: string
    requestId: string
    data: {
      requestId: string
      model: string
      usage: {
        promptTokens: number
        completionTokens: number
        totalTokens: number
      }
      choices: {
        index: number
        message: {
          role: MessageRole
          content: string
        }
        finishReason: string
      }[]
    }
  }

  /** 批量触发请求参数 */
  export interface BatchTriggerRequest {
    materials: Service.Forward.MaterialPlatform.MaterialIdentifierParams[]
    options: {
      invokeLangBridgeBizKey: string
      invokeLangBridgeOperator: string
    }
  }

  /** 批量触发单个结果 */
  export interface BatchTriggerItemResult {
    material: Service.Forward.MaterialPlatform.MaterialIdentifierParams
    success: boolean
    jobId?: number
    error?: string
  }

  /** 批量触发响应结果 */
  export interface BatchTriggerResponse {
    total: number
    succeeded: number
    failed: number
    results: BatchTriggerItemResult[]
  }

  /**
   * 通用查询作业的选项
   */
  export interface FindJobOptions {
    id?: number
    materialId?: string
    namespace?: string
    materialVersion?: string
    materialPubId?: number
    state?: number // 默认为 1
  }

  /**
   * 通用查询多个作业的选项
   */
  export interface FindJobsOptions extends FindJobOptions {
    limit?: number
    offset?: number
  }
}
