import { Controller, Get, Post, Body, Param, Query, Logger } from '@nestjs/common'
import { MaterialSmartDescriptionJobService } from './material-smart-description-job.service'
import { ForwardMaterialPlatformService } from '../forward/material-platform/material-platform.service'
import { MaterialSmartDescriptionJob } from '../../database/models'
import { MaterialSmartDescriptionJobStaging } from '@/database/models/material-smart-description-job.model'
import { fetchFromCDN } from '../../tools/cdn'

// 物料 Schema 接口定义
interface MaterialSchema {
  componentName: string
  componentChineseName?: string
  [key: string]: unknown
}

interface MaterialDetailInfo {
  title: string
  name: string
  namespace: string
}

interface EnrichedJob {
  id: number
  materialId: number
  materialVersion: string
  materialPubId: number
  resultId?: number | null
  staging: string
  rawConversation?: string | null
  rawResult?: string | null
  failedReason?: string | null
  createTime: number
  state: number
  materialDetail: MaterialDetailInfo
}

@Controller('material-smart-description/job')
export class MaterialSmartDescriptionJobController {
  private readonly logger = new Logger(MaterialSmartDescriptionJobController.name)

  constructor(
    private readonly jobService: MaterialSmartDescriptionJobService,
    private readonly materialPlatformService: ForwardMaterialPlatformService,
  ) {}

  /**
   * 从 CDN 获取物料 Schema 数据
   */
  private async getSchemaFromCDN(schemaUrl: string): Promise<MaterialSchema | null> {
    try {
      if (!schemaUrl) {
        return null
      }

      this.logger.verbose(`从 CDN 获取 schema: ${schemaUrl}`)
      const schema = await fetchFromCDN<MaterialSchema>(schemaUrl)

      if (!schema || typeof schema !== 'object') {
        this.logger.warn(`Schema 数据格式不正确: ${schemaUrl}`)
        return null
      }

      return schema
    }
    catch (error) {
      this.logger.error(`从 CDN 获取 schema 失败: ${schemaUrl}`, error)
      return null
    }
  }

  /**
   * 从 Schema 数据中提取物料基础信息
   */
  private extractMaterialDetailFromSchema(
    schema: MaterialSchema,
    namespace: string,
  ): MaterialDetailInfo {
    return {
      title: schema.componentChineseName || schema.componentName || '未知物料',
      name: schema.componentName || 'unknown',
      namespace: namespace || '未知',
    }
  }

  /**
   * 获取物料基础信息（优先从 schemaUrl 获取）
   */
  private async getMaterialDetailInfo(
    job: MaterialSmartDescriptionJob,
  ): Promise<MaterialDetailInfo> {
    // 优先从 schemaUrl 获取
    if (job.schemaUrl) {
      try {
        const schema = await this.getSchemaFromCDN(job.schemaUrl)
        if (schema) {
          return this.extractMaterialDetailFromSchema(schema, job.namespace)
        }
      }
      catch (error) {
        this.logger.warn(`从 schema 获取物料信息失败，fallback 到原方法: ${error.message}`)
      }
    }

    // fallback 到原方法
    try {
      const materialDetail = await this.materialPlatformService.getMaterialVersionDetail({
        materialId: job.materialId,
        materialVersionName: job.materialVersion,
      })
      return {
        title: materialDetail.title,
        name: materialDetail.currentVersion.schema.componentName,
        namespace: materialDetail.namespace,
      }
    }
    catch (error) {
      this.logger.error(`获取物料详情失败: ${job.materialId}`, error)
      return {
        title: '未知物料',
        name: 'unknown',
        namespace: '未知',
      }
    }
  }

  @Post('trigger')
  async trigger(
    @Body()
    body: {
      material: Service.Forward.MaterialPlatform.MaterialIdentifierParams
      options: {
        invokeLangBridgeBizKey: string
        invokeLangBridgeOperator: string
      }
    },
  ): Promise<number> {
    return this.jobService.trigger(body.material, body.options)
  }

  /**
   * 批量触发智能描述生成任务
   * 并发执行，单个失败不影响整体流程
   */
  @Post('trigger/batch')
  async triggerBatch(
    @Body() body: Service.MaterialSmartDescription.Job.BatchTriggerRequest,
  ): Promise<Service.MaterialSmartDescription.Job.BatchTriggerResponse> {
    return this.jobService.triggerBatch(body.materials, body.options)
  }

  /**
   * 查询指定物料最新的 job 进度
   */
  @Post('stage')
  async getLatestJobStage(
    @Body()
    body: {
      material: Service.Forward.MaterialPlatform.MaterialIdentifierParams
    },
  ): Promise<{ stage: MaterialSmartDescriptionJobStaging }> {
    const stage = await this.jobService.getLatestJobStageByMaterial(
      body.material,
    )
    return { stage }
  }

  /**
   * 根据 namespace 获取作业列表（支持模糊查询）
   */
  @Get('namespace/:namespace')
  async getJobsByNamespace(
    @Param('namespace') namespace: string,
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<MaterialSmartDescriptionJob>> {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize

    // 并行获取数据和总数
    const [jobs, total] = await Promise.all([
      this.jobService.findJobsByNamespace(namespace, pageSize, offset),
      this.jobService.countJobsByNamespace(namespace),
    ])

    const totalPage = Math.ceil(total / pageSize)

    return {
      list: jobs,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 分页获取作业列表
   */
  @Get()
  async getJobs(
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<EnrichedJob>> {
    const { pageNum = 1, pageSize = 10 } = query
    const offset = (pageNum - 1) * pageSize

    // 使用兼容 MySQL 5.7 的查询方式，通过子查询获取每个 material_pub_id 的最新记录
    const sql = `
      SELECT j1.*
      FROM material_smart_description_job j1
      INNER JOIN (
        SELECT material_pub_id, MAX(create_time) as max_create_time
        FROM material_smart_description_job
        WHERE state = 1
        GROUP BY material_pub_id
      ) j2 ON j1.material_pub_id = j2.material_pub_id AND j1.create_time = j2.max_create_time
      WHERE j1.state = 1
      ORDER BY j1.create_time DESC
      LIMIT ${pageSize} OFFSET ${offset}
    `

    const countSql = `
      SELECT COUNT(DISTINCT material_pub_id) as total
      FROM material_smart_description_job
      WHERE state = 1
    `

    const [jobs, countResult] = await Promise.all([
      this.jobService.executeRawQuery(sql),
      this.jobService.executeRawQuery(countSql),
    ])

    const total = (countResult as { total: number }[])[0]?.total || 0
    const totalPage = Math.ceil(total / pageSize)

    // 获取物料基础信息
    const enrichedJobs = await Promise.all(
      (
        jobs as {
          id: number
          material_id: number
          material_version: string
          material_pub_id: number
          result_id?: number | null
          staging: number
          raw_conversation?: string | null
          raw_result?: string | null
          failed_reason?: string | null
          create_time: number
          state: number
          namespace?: string
          schema_url?: string | null
        }[]
      ).map(async (job): Promise<EnrichedJob> => {
        // 构造 MaterialSmartDescriptionJob 对象
        const jobObj = {
          id: job.id,
          materialId: job.material_id,
          materialVersion: job.material_version,
          materialPubId: job.material_pub_id,
          resultId: job.result_id,
          staging: job.staging as unknown as MaterialSmartDescriptionJobStaging,
          rawConversation: job.raw_conversation,
          rawResult: job.raw_result,
          failedReason: job.failed_reason,
          createTime: job.create_time,
          state: job.state,
          namespace: job.namespace || '未知',
          schemaUrl: job.schema_url,
        } as MaterialSmartDescriptionJob

        const materialDetail = await this.getMaterialDetailInfo(jobObj)

        return {
          id: job.id,
          materialId: job.material_id,
          materialVersion: job.material_version,
          materialPubId: job.material_pub_id,
          resultId: job.result_id,
          staging: job.staging as unknown as string,
          rawConversation: job.raw_conversation,
          rawResult: job.raw_result,
          failedReason: job.failed_reason,
          createTime: job.create_time,
          state: job.state,
          materialDetail,
        }
      }),
    )

    return {
      list: enrichedJobs,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 获取作业统计信息
   */
  @Get('stats/overview')
  async getJobStats(): Promise<{
    pending: number
    succeeded: number
    failed: number
    total: number
  }> {
    return this.jobService.getJobStats()
  }

  /**
   * 根据物料 ID 获取作业列表
   */
  @Get('material/:materialId')
  async getJobsByMaterialId(
    @Param('materialId') materialId: string,
    @Query('limit') limit: string = '10',
    @Query('offset') offset: string = '0',
  ): Promise<MaterialSmartDescriptionJob[]> {
    return this.jobService.findJobsByMaterialId(
      materialId,
      parseInt(limit),
      parseInt(offset),
    )
  }

  /**
   * 根据 materialPubId 获取历史记录
   */
  @Get('history/:materialPubId')
  async getHistoryByMaterialPubId(
    @Param('materialPubId') materialPubId: string,
    @Query() query: Service.PaginationParams,
  ): Promise<Service.PaginationResult<EnrichedJob>> {
    const { pageNum = 1, pageSize = 20 } = query
    const offset = (pageNum - 1) * pageSize

    const { jobs, total } = await this.jobService.findJobsByMaterialPubId(
      parseInt(materialPubId),
      pageSize,
      offset,
    )

    const totalPage = Math.ceil(total / pageSize)

    // 获取物料基础信息
    const enrichedJobs = await Promise.all(
      jobs.map(async (job): Promise<EnrichedJob> => {
        const materialDetail = await this.getMaterialDetailInfo(job)

        return {
          id: job.id,
          materialId: job.materialId,
          materialVersion: job.materialVersion,
          materialPubId: job.materialPubId,
          resultId: job.resultId,
          staging: job.staging,
          rawConversation: job.rawConversation,
          rawResult: job.rawResult,
          failedReason: job.failedReason,
          createTime: job.createTime,
          state: job.state,
          materialDetail,
        }
      }),
    )

    return {
      list: enrichedJobs,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }
}
