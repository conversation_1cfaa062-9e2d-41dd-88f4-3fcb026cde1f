import * as assert from 'assert'
import {
  mkdir,
  readdir,
  readFile,
  constants as fsConstants,
  access,
  writeFile,
  rm,
} from 'fs/promises'
import { Injectable, Logger } from '@nestjs/common'
import { join, resolve } from 'path'
import { PackageJson } from 'type-fest'
import simpleGit, { SimpleGit } from 'simple-git'
import { XMLParser, XMLValidator } from 'fast-xml-parser'

import { PROJECT_ROOT } from '@/constants/path'
import { convertToOAuthGitUrl } from '@/tools/git'

interface Message {
  /** 消息角色 */
  role: 'user' | 'assistant'
  /** 文本内容 */
  content: string
  /** 图片 URL（可选） */
  imageUrl?: string
}

interface LLMConversation {
  role: string // 对话角色：用户或助手
  content: string // 对话内容
  timestamp: number // 对话时间戳
  file?: string // 添加可选的文件字段
}

interface ProjectStructure {
  packageJson: PackageJson
  fileTree: string
}

export interface AnalysisContext {
  projectBasicInfo: {
    jobId: string
    gitUrl: string
    exportName: string
    namespace: string
  }
  langBridgeOptions: {
    bizKey: string
    operator: string
  }
  projectAnalysisData: {
    projectDir: string // 临时目录
    projectStructure: ProjectStructure // 项目结构
    conversations: LLMConversation[] // 对话记录
    fileContents: Map<string, string> // 文件内容缓存
    analyzedFiles: Set<string> // 已分析的文件
  }
}

// 修改接口定义
interface AnalysisResponse {
  currentAnalysis: string
  requiresAdditionalFiles: boolean
  filesToAnalyze: string[]
  reason: string
  isComplete: boolean
  summary: string
}

@Injectable()
export class LLMReadmeGeneratorAgentService {
  private readonly logger = new Logger(LLMReadmeGeneratorAgentService.name)
  private readonly git: SimpleGit = simpleGit()
  private readonly XML_MARKER_START = `<analysis>`
  private readonly XML_MARKER_END = `</analysis>`
  private readonly xmlParser: XMLParser = new XMLParser({
    ignoreAttributes: true,
    parseTagValue: true,
    trimValues: true,
    cdataPropName: '__cdata',
  })

  static project_temp_dir = resolve(PROJECT_ROOT, 'ai_project_temp')

  static get DOMAIN(): string {
    if (process.env.NODE_ENV === 'development') {
      return 'https://lego.corp.kuaishou.com'
    }
    else {
      return 'http://kwaishop-gateway-manage.internal'
    }
  }

  /**
   * 处理流式响应的内部方法
   */
  private async processStreamResponse(
    url: string,
    requestData: {
      messages: Service.MaterialSmartDescription.Job.Message[]
      model: string
      bizKey: string
      operator?: string
    },
  ): Promise<Service.MaterialSmartDescription.Job.CallModelResult> {
    return new Promise((resolve, reject) => {
      const processStream = async (): Promise<void> => {
        try {
          const response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'text/event-stream',
            },
            body: JSON.stringify(requestData),
          })

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          if (!response.body) {
            throw new Error('No response body')
          }

          const stream = response.body as ReadableStream<Uint8Array>
          const reader = stream.getReader()
          const decoder = new TextDecoder()

          let buffer = ''
          let finalResult: Service.MaterialSmartDescription.Job.CallModelResult | null = null
          let accumulatedContent = '' // 累积的内容

          try {
            while (true) {
              const { done, value } = await reader.read()

              if (done) {
                break
              }

              const text = decoder.decode(value, { stream: true })
              buffer += text

              // 检查错误事件
              if (text.startsWith('event:error')) {
                const errorMessage = text.replace('event:error', '').trim()
                throw new Error(`Stream processing error: ${errorMessage}`)
              }

              // 使用正则表达式分割消息，保留结尾的换行符
              const messages = buffer.split(/\n(?=data:)/)

              // 处理除最后一条外的所有完整消息
              buffer = messages.pop() || '' // 保存最后一条可能不完整的消息

              for (const message of messages) {
                if (message.trim()) {
                  const lines = message.trim().split('\n')
                  for (const line of lines) {
                    if (line.startsWith('data:')) {
                      try {
                        const jsonStr = line.slice(5).trim()
                        if (jsonStr) {
                          const data = JSON.parse(jsonStr)
                          // 处理流式数据
                          if (data && typeof data === 'object') {
                            const streamData = data as Service.MaterialSmartDescription.Job.CallModelResult

                            // 如果是第一次接收数据，初始化 finalResult
                            if (!finalResult) {
                              finalResult = { ...streamData }
                              // 初始化累积内容
                              if (streamData.data?.choices?.[0]?.message?.content) {
                                accumulatedContent = streamData.data.choices[0].message.content
                                this.logger.debug(`Stream: 初始化内容，长度: ${accumulatedContent.length}`)
                              }
                            }
                            else {
                              // 累积内容
                              if (streamData.data?.choices?.[0]?.message?.content) {
                                const newContent = streamData.data.choices[0].message.content
                                accumulatedContent += newContent
                                this.logger.debug(`Stream: 累积内容，新增长度: ${newContent.length}，总长度: ${accumulatedContent.length}`)
                              }

                              // 更新其他字段（如 usage 等）
                              if (streamData.data?.usage) {
                                finalResult.data.usage = streamData.data.usage
                              }
                              if (streamData.data?.choices?.[0]?.finishReason) {
                                finalResult.data.choices[0].finishReason = streamData.data.choices[0].finishReason
                                this.logger.debug(`Stream: 接收到 finishReason: ${streamData.data.choices[0].finishReason}`)
                              }
                            }
                          }
                        }
                      }
                      catch (e) {
                        this.logger.warn(`Failed to parse SSE data: ${(e as Error).message}`)
                      }
                    }
                  }
                }
              }
            }

            if (finalResult) {
              // 将累积的内容设置到最终结果中
              if (finalResult.data?.choices?.[0]?.message) {
                finalResult.data.choices[0].message.content = accumulatedContent
                this.logger.debug(`Stream: 最终合并内容长度: ${accumulatedContent.length}`)
              }
              resolve(finalResult)
            }
            else {
              throw new Error('No valid response data received from stream')
            }
          }
          finally {
            reader.releaseLock()
          }
        }
        catch (error: unknown) {
          if (error instanceof Error && error.name === 'AbortError') {
            reject(new Error('Request was aborted'))
          }
          else {
            reject(error instanceof Error ? error : new Error(String(error)))
          }
        }
      }

      processStream()
    })
  }

  async callModel(
    params: Service.MaterialSmartDescription.Job.CallModelParams,
  ): Promise<Service.MaterialSmartDescription.Job.CallModelResult> {
    const { messages, options } = params
    const { model = 'claude-3.7-sonnet', bizKey, operator, retry } = options

    // 重试配置默认值
    const retryConfig = {
      maxRetries: retry?.maxRetries ?? 3,
      retryDelay: retry?.retryDelay ?? 1000,
      backoffFactor: retry?.backoffFactor ?? 2,
      maxRetryDelay: retry?.maxRetryDelay ?? 10000,
    }

    let lastError: Error

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        const url = `${LLMReadmeGeneratorAgentService.DOMAIN}/gateway/langbridge/model/service/stream`
        const requestData = {
          messages,
          model,
          bizKey,
          operator,
        }

        this.logger.log(
          `Calling LLM model service (attempt ${attempt + 1}/${
            retryConfig.maxRetries + 1
          }): ${url}`,
        )
        this.logger.debug(`Request data: ${JSON.stringify(requestData)}`)

        const result = await this.processStreamResponse(url, requestData)

        this.logger.log('LLM model service stream response completed successfully')
        return result
      }
      catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))

        this.logger.warn(`Attempt ${attempt + 1} failed:`, lastError.message)

        // 如果是最后一次尝试，或者错误不应该重试，直接抛出错误
        if (attempt === retryConfig.maxRetries) {
          this.logger.error(
            'Failed to call LLM model service after all retries:',
            lastError,
          )
          throw lastError
        }

        // 计算重试延迟时间（指数退避）
        const delay = Math.min(
          retryConfig.retryDelay * Math.pow(retryConfig.backoffFactor, attempt),
          retryConfig.maxRetryDelay,
        )

        this.logger.log(`Retrying in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    // 理论上不会到达这里，但为了类型安全
    // @ts-expect-error 类型错误
    throw lastError
  }

  async analyzeMaterial(
    jobId: string,
    gitUrl: string,
    exportName: string,
    agentNeedContext: {
      namespace: string
    },
    options: {
      invokeLangBridgeBizKey: string
      invokeLangBridgeOperator: string
    },
  ): Promise<{
      rawConversation: string
      rawResult: string
      result: Service.MaterialSmartDescription.BasicMaterialDescription
    }> {
    const { invokeLangBridgeBizKey, invokeLangBridgeOperator } = options
    assert(invokeLangBridgeBizKey, 'invokeLangBridgeBizKey is required')
    assert(invokeLangBridgeOperator, 'invokeLangBridgeOperator is required')

    const { namespace } = agentNeedContext

    const projectDir = join(
      LLMReadmeGeneratorAgentService.project_temp_dir,
      jobId,
    )

    try {
      this.logger.verbose(`开始分析导出项: ${exportName}`)

      // 1. 初始化分析上下文
      const context: AnalysisContext = {
        projectBasicInfo: {
          jobId,
          gitUrl,
          exportName,
          namespace,
        },
        langBridgeOptions: {
          bizKey: invokeLangBridgeBizKey,
          operator: invokeLangBridgeOperator,
        },
        projectAnalysisData: {
          projectDir,
          projectStructure: await this.initializeProject(gitUrl, projectDir),
          conversations: [],
          fileContents: new Map<string, string>(),
          analyzedFiles: new Set<string>(),
        },
      }

      this.logger.verbose(
        `项目初始化完成，临时目录: ${context.projectAnalysisData.projectDir}`,
      )

      // 2. 获取入口文件
      const entryFile = await this.getEntryFile(
        context.projectAnalysisData.projectStructure,
        context.projectAnalysisData.projectDir,
        context,
      )
      this.logger.verbose(`找到入口文件: ${entryFile}`)

      // 3. 开始多轮分析对话
      this.logger.verbose('开始多轮分析对话')
      const finalAnalysis = await this.conductAnalysisDialogue(
        entryFile,
        context,
      )

      // 4. 保存分析结果和对话记录
      await this.saveAnalysisResults(context, finalAnalysis)

      this.logger.verbose('分析完成')

      // 删除目录
      await this.deleteProjectDir(context.projectAnalysisData.projectDir)
      return {
        rawConversation: context.projectAnalysisData.conversations
          .map(c => c.content)
          .join('\n'),
        rawResult: finalAnalysis,
        result: this.parseRawAnalysisResult(finalAnalysis),
      }
    }
    catch (error: unknown) {
      this.logger.error('分析过程出错:', error)
      throw error instanceof Error ? error : new Error('分析过程出错')
    }
  }

  private _parseXmlWithRegex(
    rawXmlString: string,
  ): Service.MaterialSmartDescription.BasicMaterialDescription {
    this.logger.verbose(
      `_parseXmlWithRegex received for parsing: >>>${rawXmlString}<<<`,
    )

    if (typeof rawXmlString !== 'string' || rawXmlString.length === 0) {
      this.logger.error('rawXmlString is not a valid string or is empty.')
      // Return a default, empty structure conforming to the type
      return {
        exportType: { type: '', explanation: '' },
        functionality: { features: [] },
        scenarios: { cases: [] },
        uiStructure: { styleFeatures: [] },
        usage: { basicExample: [] },
        api: {
          parameters: { typescriptCode: '' },
          returnValue: { type: '', description: '' },
        },
        considerations: { limitations: [], performance: [] },
        bestPractices: { recommendations: [], antiPatterns: [] },
        decisiveFactors: { advantages: [], limitations: [] },
      }
    }

    const trimmedRawXmlString = rawXmlString.trim()

    const result: Partial<Service.MaterialSmartDescription.BasicMaterialDescription>
      = {}

    // Helper to extract content from a tag e.g., <tagName>content</tagName>
    const extractTagContent = (
      xml: string,
      tagName: string,
    ): string | undefined => {
      const regexPatternString = `<${tagName}>([\\s\\S]*?)<\\/${tagName}>`

      try {
        const regex = new RegExp(regexPatternString)
        const match = xml.match(regex)
        if (match && match[1]) {
          let content = match[1].trim()
          if (content.startsWith('<![CDATA[') && content.endsWith(']]>')) {
            content = content.substring(9, content.length - 3).trim()
          }
          return content
        }
      }
      catch (e: unknown) {
        if (e instanceof Error) {
          this.logger.error(
            `Error creating or using RegExp for tagName '${tagName}': ${e.message}`,
          )
        }
        else {
          this.logger.error(
            `Error creating or using RegExp for tagName '${tagName}': ${String(
              e,
            )}`,
          )
        }
      }
      return undefined
    }

    // Helper to extract <item>...</item> contents from a block of XML that contains them
    const extractItemsFromListContent = (
      listContent: string | undefined,
    ): string[] => {
      if (!listContent) return []
      const items: string[] = []
      const itemRegex = /<item>([\s\S]*?)<\/item>/g
      let match
      while ((match = itemRegex.exec(listContent)) !== null) {
        let itemContent = match[1].trim()
        if (
          itemContent.startsWith('<![CDATA[')
          && itemContent.endsWith(']]>')
        ) {
          itemContent = itemContent.substring(9, itemContent.length - 3).trim()
        }
        items.push(itemContent)
      }
      return items
    }

    // Pass the trimmed string for initial parsing
    const analysisContent = extractTagContent(trimmedRawXmlString, 'analysis')

    if (!analysisContent) {
      this.logger.error(
        'Could not find or parse <analysis> tag content from raw XML string during regex parsing.',
      )
      // Return a default, empty structure conforming to the type
      return {
        exportType: { type: '', explanation: '' },
        functionality: { features: [] },
        scenarios: { cases: [] },
        uiStructure: { styleFeatures: [] },
        usage: { basicExample: [] },
        api: {
          parameters: { typescriptCode: '' },
          returnValue: { type: '', description: '' },
        },
        considerations: { limitations: [], performance: [] },
        bestPractices: { recommendations: [], antiPatterns: [] },
        decisiveFactors: { advantages: [], limitations: [] },
      }
    }

    // 1. exportType
    const exportTypeBlockContent = extractTagContent(
      analysisContent,
      'exportType',
    )
    if (exportTypeBlockContent) {
      result.exportType = {
        type: extractTagContent(exportTypeBlockContent, 'type') || '',
        explanation:
          extractTagContent(exportTypeBlockContent, 'explanation') || '',
      }
    }
    else {
      result.exportType = { type: '', explanation: '' }
    }

    // 2. functionality
    const functionalityBlockContent = extractTagContent(
      analysisContent,
      'functionality',
    )
    if (functionalityBlockContent) {
      const featuresContent = extractTagContent(
        functionalityBlockContent,
        'features',
      )
      result.functionality = {
        features: extractItemsFromListContent(featuresContent),
      }
    }
    else {
      result.functionality = { features: [] }
    }

    // 3. scenarios
    const scenariosBlockContent = extractTagContent(
      analysisContent,
      'scenarios',
    )
    if (scenariosBlockContent) {
      const casesContent = extractTagContent(scenariosBlockContent, 'cases')
      result.scenarios = {
        cases: extractItemsFromListContent(casesContent),
      }
    }
    else {
      result.scenarios = { cases: [] }
    }

    // 4. uiStructure
    const uiStructureBlockContent = extractTagContent(
      analysisContent,
      'uiStructure',
    )
    if (uiStructureBlockContent) {
      const styleFeaturesContent = extractTagContent(
        uiStructureBlockContent,
        'styleFeatures',
      )
      result.uiStructure = {
        styleFeatures: extractItemsFromListContent(styleFeaturesContent),
      }
    }
    else {
      result.uiStructure = { styleFeatures: [] }
    }

    // 5. usage
    const usageBlockContent = extractTagContent(analysisContent, 'usage')
    if (usageBlockContent) {
      const basicExampleContent = extractTagContent(
        usageBlockContent,
        'basicExample',
      )
      result.usage = {
        basicExample: extractItemsFromListContent(basicExampleContent),
      }
    }
    else {
      result.usage = { basicExample: [] }
    }

    // 6. api
    const apiBlockContent = extractTagContent(analysisContent, 'api')
    if (apiBlockContent) {
      const parametersBlockContent = extractTagContent(
        apiBlockContent,
        'parameters',
      )
      let typescriptCode = ''
      if (parametersBlockContent) {
        // 提取 CDATA 内容或直接内容
        let content = parametersBlockContent.trim()
        if (content.startsWith('<![CDATA[') && content.endsWith(']]>')) {
          content = content.substring(9, content.length - 3).trim()
        }
        typescriptCode = content
      }

      const returnValueBlockContent = extractTagContent(
        apiBlockContent,
        'returnValue',
      )
      let returnValueData
      if (returnValueBlockContent) {
        returnValueData = {
          type: extractTagContent(returnValueBlockContent, 'type') || '',
          description:
            extractTagContent(returnValueBlockContent, 'description') || '',
        }
      }
      else {
        returnValueData = { type: '', description: '' }
      }

      result.api = {
        parameters: { typescriptCode },
        returnValue: returnValueData,
      }
    }
    else {
      result.api = {
        parameters: { typescriptCode: '' },
        returnValue: { type: '', description: '' },
      }
    }

    // 7. considerations
    const considerationsBlockContent = extractTagContent(
      analysisContent,
      'considerations',
    )
    if (considerationsBlockContent) {
      const limitationsContent = extractTagContent(
        considerationsBlockContent,
        'limitations',
      )
      const performanceContent = extractTagContent(
        considerationsBlockContent,
        'performance',
      )
      result.considerations = {
        limitations: extractItemsFromListContent(limitationsContent),
        performance: extractItemsFromListContent(performanceContent),
      }
    }
    else {
      result.considerations = { limitations: [], performance: [] }
    }

    // 8. bestPractices
    const bestPracticesBlockContent = extractTagContent(
      analysisContent,
      'bestPractices',
    )
    if (bestPracticesBlockContent) {
      const recommendationsContent = extractTagContent(
        bestPracticesBlockContent,
        'recommendations',
      )
      const antiPatternsContent = extractTagContent(
        bestPracticesBlockContent,
        'antiPatterns',
      )
      result.bestPractices = {
        recommendations: extractItemsFromListContent(recommendationsContent),
        antiPatterns: extractItemsFromListContent(antiPatternsContent),
      }
    }
    else {
      result.bestPractices = { recommendations: [], antiPatterns: [] }
    }

    // 9. decisiveFactors
    const decisiveFactorsBlockContent = extractTagContent(
      analysisContent,
      'decisiveFactors',
    )
    if (decisiveFactorsBlockContent) {
      const advantagesContent = extractTagContent(
        decisiveFactorsBlockContent,
        'advantages',
      )
      const limitationsDFContent = extractTagContent(
        decisiveFactorsBlockContent,
        'limitations',
      )
      result.decisiveFactors = {
        advantages: extractItemsFromListContent(advantagesContent),
        limitations: extractItemsFromListContent(limitationsDFContent),
      }
    }
    else {
      result.decisiveFactors = { advantages: [], limitations: [] }
    }

    return result as Service.MaterialSmartDescription.BasicMaterialDescription
  }

  parseRawAnalysisResult(
    rawAnalysisResult: string,
  ): Service.MaterialSmartDescription.BasicMaterialDescription {
    const parsed = this._parseXmlWithRegex(rawAnalysisResult)
    return parsed
  }

  private async initializeProject(
    gitUrl: string,
    dir: string,
  ): Promise<ProjectStructure> {
    await mkdir(dir, { recursive: true })

    // 将 Git URL 转换为带有 OAuth token 的 HTTPS 格式
    const oauthGitUrl = convertToOAuthGitUrl(gitUrl)

    this.logger.verbose(`克隆仓库中... 原始URL: ${gitUrl}`)
    this.logger.verbose(`使用OAuth URL: ${oauthGitUrl}`)

    try {
      await this.git.clone(oauthGitUrl, dir)
      this.logger.verbose('仓库克隆成功')
    }
    catch (error) {
      this.logger.error('仓库克隆失败:', error)
      throw new Error(`克隆仓库失败: ${error instanceof Error ? error.message : String(error)}`)
    }

    return await this.analyzeProjectStructure(dir)
  }

  private async analyzeProjectStructure(
    dir: string,
  ): Promise<ProjectStructure> {
    try {
      const packageJsonPath = join(dir, 'package.json')
      this.logger.debug(`读取 package.json: ${packageJsonPath}`)

      const packageJson = JSON.parse(
        await readFile(packageJsonPath, 'utf-8'),
      ) as PackageJson

      let fileTree = ''
      async function buildTree(directory: string, prefix = ''): Promise<void> {
        const files = await readdir(directory, { withFileTypes: true })

        for (const file of files) {
          if (file.name.startsWith('.') || file.name === 'node_modules')
            continue

          fileTree += `${prefix}${file.name}\n`

          if (file.isDirectory()) {
            await buildTree(join(directory, file.name), `${prefix}  `)
          }
        }
      }

      await buildTree(dir)
      return { packageJson, fileTree }
    }
    catch (error) {
      if (error instanceof Error) {
        this.logger.error('分析项目结构失败', error)
      }
      throw error
    }
  }

  private async getEntryFile(
    projectStructure: ProjectStructure,
    projectDir: string,
    context: AnalysisContext,
  ): Promise<string> {
    const prompt = `[角色] 你是一个专业的前端项目分析专家，现在需要你帮助确定组件库的源代码入口文件。
这是整个分析流程的第一步，找到正确的入口文件对后续分析至关重要。

[任务目标]
1. 根据 package.json 内容和项目结构，找出组件库的源代码入口文件
2. 该文件将作为后续分析其他组件文件的起点
3. 必须确保返回的是文件地址是提供的项目文件结构中存在的文件，而不是构建产物

[注意事项]
1. 必须返回文件目录树中实际存在的文件路径
2. package.json 中的 main/module/exports 通常指向构建后的文件，需要根据这些文件反推源代码的入口文件
3. 常见的源代码入口文件规律：
    - 如果 main 指向 'dist/index.js'，源码可能在 'src/index.ts'
    - 如果 main 指向 'lib/index.js'，源码可能在 'src/index.tsx' 或 'src/index.ts'
    - 如果 main 指向 'es/index.js'，查找对应的 'src' 目录下的源文件

[输入信息]
package.json 内容：
${JSON.stringify(projectStructure.packageJson, null, 2)}

项目文件结构：
${projectStructure.fileTree}

[输出要求]
仅返回一个在文件目录树中实际存在的源代码文件路径（.ts, .tsx, .js, .jsx）。
不要返回构建产物路径（如 dist/, lib/, es/ 等目录下的文件）。
将分析得到的入口地址放在 XML 中

[返回示例]
${this.XML_MARKER_START}
<entry>src/index.ts</entry>
${this.XML_MARKER_END}`

    context.projectAnalysisData.conversations.push({
      role: 'user',
      content: prompt,
      timestamp: Date.now(),
    })

    const response = await this.callModel({
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      options: context.langBridgeOptions,
    })
    console.log('response', response)

    const suggestedEntry = await this.extractEntryFileFromResponse(
      response.data.choices[0].message.content,
    )

    context.projectAnalysisData.conversations.push({
      role: 'assistant',
      content: suggestedEntry,
      timestamp: Date.now(),
    })

    // 验证 LLM 返回的文件是否在文件系统中实际存在
    if (!(await this.isFileExists(suggestedEntry, projectDir))) {
      this.logger.warn(
        `LLM 建议的入口文件 ${suggestedEntry} 不存在，尝试二次确认`,
      )

      // 二次确认提示
      const confirmationPrompt = `
之前建议的入口文件 '${suggestedEntry}' 在文件树中不存在。
请重新查看文件树，只返回实际存在的文件路径：

${projectStructure.fileTree}

请仔细检查文件树，只返回一个确实存在的源代码文件路径。`

      const confirmationResponse = await this.callModel({
        messages: [
          {
            role: 'user',
            content: confirmationPrompt,
          },
        ],
        options: context.langBridgeOptions,
      })

      const confirmedEntry = await this.extractEntryFileFromResponse(
        confirmationResponse.data.choices[0].message.content?.trim() ?? '',
      )

      if (await this.isFileExists(confirmedEntry, projectDir)) {
        this.logger.verbose(`找到有效的入口文件: ${confirmedEntry}`)
        return confirmedEntry
      }
    }
    else {
      this.logger.verbose(`找到有效的入口文件: ${suggestedEntry}`)
      return suggestedEntry
    }

    // 如果 LLM 无法找到有效的入口文件，使用启发式搜索
    this.logger.warn('LLM 未能找到有效的入口文件，使用启发式搜索')
    const entryFile = await this.findEntryFileHeuristically(
      projectStructure.fileTree,
      projectDir,
    )

    if (!entryFile) {
      throw new Error('无法找到有效的入口文件')
    }

    return entryFile
  }

  private async findEntryFileHeuristically(
    fileTree: string,
    tempDir: string,
  ): Promise<string | null> {
    // 常见的源码入口文件模式
    const patterns = [
      /src\/index\.tsx?$/,
      /src\/index\.jsx?$/,
      /components\/index\.tsx?$/,
      /components\/index\.jsx?$/,
      /src\/main\.tsx?$/,
      /src\/main\.jsx?$/,
    ]

    const fileTreeLines = fileTree.split('\n').map(line => line.trim())

    for (const pattern of patterns) {
      const matchedFile = fileTreeLines
        .map(line => line.replace(/[│├─└\s]/g, '').trim())
        .find(file => pattern.test(file))

      if (matchedFile && (await this.isFileExists(matchedFile, tempDir))) {
        this.logger.verbose(`通过启发式搜索找到入口文件: ${matchedFile}`)
        return matchedFile
      }
    }

    return null
  }

  private async extractEntryFileFromResponse(
    response: string,
  ): Promise<string> {
    try {
      const pattern = new RegExp(
        `${this.XML_MARKER_START}([\\s\\S]*?)${this.XML_MARKER_END}`,
      )
      const match = response.match(pattern)

      if (!match || !match[1]) {
        this.logger.error('未找到 XML 响应标记')
        throw new Error('响应格式错误：未找到 XML 标记')
      }

      const xmlContent = `<root>${match[1].trim()}</root>`

      // 验证 XML 格式
      if (!XMLValidator.validate(xmlContent)) {
        throw new Error('XML 格式无效')
      }

      const parsed = this.xmlParser.parse(xmlContent)
      const root = parsed.root

      return root.entry
    }
    catch (error: unknown) {
      this.logger.error('解析 XML 响应失败:', error)
      throw error instanceof Error ? error : new Error('XML 解析失败')
    }
  }

  private async conductAnalysisDialogue(
    entryFile: string,
    context: AnalysisContext,
  ): Promise<string> {
    const MAX_ROUNDS = 50
    let currentRound = 0

    // 初始化对话消息数组
    const messages: Message[] = []

    // 第一轮对话：分析入口文件
    this.logger.verbose(`开始分析入口文件: ${entryFile}`)
    const initialContent = await this.getFileContent(entryFile, context)

    const initialPrompt = `[角色] 你是一个专业的前端组件分析专家，正在分析一个组件库中的特定导出项。

[重要规则]
1. 所有返回的文件路径必须是项目中实际存在的物理路径
2. 不要使用项目中配置的路径别名（如 @/components）
3. 所有路径都必须是相对于项目根目录的相对路径（如 src/components/button.ts）

[任务目标]
分析导出项 "${context.projectBasicInfo.exportName}" 的实现和使用方式。

[当前文件]
路径: ${entryFile}
内容:
\`\`\`
${initialContent}
\`\`\`

[项目文件结构]
${context.projectAnalysisData.projectStructure.fileTree}

[分析要求]
1. 确认该文件是否包含或使用了导出项 "${context.projectBasicInfo.exportName}"
2. 如果包含该导出项：
    - 分析其实现细节
    - 提取相关的类型定义
    - 说明使用方式和注意事项
3. 确认是否需要查看其他文件来完整理解这个导出项

[输出格式]
请将分析结果包含在 XML 标记中：
${this.XML_MARKER_START}
  <currentAnalysis>当前文件的分析结果</currentAnalysis>
  <requiresAdditionalFiles>true/false</requiresAdditionalFiles>
  <filesToAnalyze>
    <item>需要分析的文件路径1</item>
    <item>需要分析的文件路径2</item>
  </filesToAnalyze>
  <reason>为什么需要分析这些额外文件</reason>
  <isComplete>true/false</isComplete>
  <summary>目前对${context.projectBasicInfo.exportName}的理解总结</summary>
${this.XML_MARKER_END}`

    // 记录对话
    messages.push({
      role: 'user',
      content: initialPrompt,
    })
    context.projectAnalysisData.conversations.push({
      role: 'user',
      content: initialPrompt,
      timestamp: Date.now(),
      file: entryFile,
    })

    let currentAnalysis = await this.callModel({
      messages,
      options: {
        ...context.langBridgeOptions,
        retry: {
          maxRetries: 5, // 首次分析使用较少的重试次数
        },
      },
    })

    let responseContent = currentAnalysis.data.choices[0].message.content ?? ''
    messages.push({
      role: 'assistant',
      content: responseContent,
    })
    context.projectAnalysisData.conversations.push({
      role: 'assistant',
      content: responseContent,
      timestamp: Date.now(),
      file: entryFile,
    })

    let result = await this.extractXmlFromResponse(responseContent)
    context.projectAnalysisData.analyzedFiles.add(entryFile)

    // 继续多轮对话，直到分析完成
    while (result.requiresAdditionalFiles && result.filesToAnalyze.length > 0) {
      if (currentRound >= MAX_ROUNDS) {
        this.logger.warn('达到最大对话轮数限制，提前结束分析')
        break
      }
      currentRound++

      try {
        const newFiles = result.filesToAnalyze.filter((file: string) => {
          const exists = this.isFileExists(
            file,
            context.projectAnalysisData.projectDir,
          )
          if (!exists) {
            this.logger.warn(`跳过不存在的文件: ${file}`)
          }
          return !context.projectAnalysisData.analyzedFiles.has(file) && exists
        })

        if (newFiles.length === 0) break

        this.logger.verbose(
          `第 ${currentRound} 轮分析，正在处理文件: ${newFiles.join(', ')}`,
        )

        // 读取新文件的内容
        const newFileContents = await Promise.all(
          newFiles.map(async (file: string) => ({
            file,
            content: await this.getFileContent(file, context),
          })),
        )

        // 构建下一轮对话的提示词
        const nextPrompt = `基于之前的分析，我们继续深入理解项目导出的 "${
          context.projectBasicInfo.exportName
        }"。

[新的文件内容]
<new-files-content>
${newFileContents
  .map(
    ({ file, content }) => `
  <file path=${file}>
${content}
  </file>`,
  )
  .join('\n')}
</new-files-content>

[分析要求]
1. 分析这些新文件如何帮助理解 "${context.projectBasicInfo.exportName}"
2. 更新对 "${context.projectBasicInfo.exportName}" 的理解
3. 确认是否还需要查看其他文件
4. 如果需要其他文件，请确保提供的文件路径是实际存在的

[输出格式]
请将分析结果包含在 XML 标记中：
${this.XML_MARKER_START}
  <currentAnalysis>对新文件的分析</currentAnalysis>
  <requiresAdditionalFiles>true/false</requiresAdditionalFiles>
  <filesToAnalyze>
    <item>需要分析的文件路径1</item>
    <item>需要分析的文件路径2</item>
  </filesToAnalyze>
  <reason>为什么需要分析这些额外文件</reason>
  <isComplete>true/false</isComplete>
  <summary>目前对${context.projectBasicInfo.exportName}的完整理解总结</summary>
${this.XML_MARKER_END}`

        messages.push({
          role: 'user',
          content: nextPrompt,
        })
        context.projectAnalysisData.conversations.push({
          role: 'user',
          content: nextPrompt,
          timestamp: Date.now(),
          file: newFiles.join(', '),
        })

        // 使用重试包装函数
        currentAnalysis = await this.callModel({
          messages,
          options: {
            ...context.langBridgeOptions,
            retry: {
              maxRetries: 5, // 首次分析使用较少的重试次数
            },
          },
        })

        responseContent = currentAnalysis.data.choices[0].message.content ?? ''
        messages.push({
          role: 'assistant',
          content: responseContent,
        })
        context.projectAnalysisData.conversations.push({
          role: 'assistant',
          content: responseContent,
          timestamp: Date.now(),
          file: newFiles.join(', '),
        })

        result = await this.extractXmlFromResponse(responseContent)
        newFiles.forEach((file: string) =>
          context.projectAnalysisData.analyzedFiles.add(file),
        )
      }
      catch (error) {
        if (error instanceof Error) {
          this.logger.error('分析过程中断:', error)
        }
        return result.summary || '分析过程中断，返回部分结果'
      }
    }

    this.logger.verbose(`文件分析完成，开始生成总结报告。`)

    // 最终总结
    const finalPrompt = `现在我们已经分析了所有相关文件，请按照以下 XML 结构提供一个关于 "${context.projectBasicInfo.exportName}"(namespace: ${context.projectBasicInfo.namespace}) 的完整总结：

${this.XML_MARKER_START}
  <exportType>
    <type>Component|Function|Class|Hook|Type|Other</type>
    <explanation>这里应当经可能完备的描述并介绍导出项具备什么功能。这里的内容主要用于方便用户准确快速决策是否应该使用当前导出项</explanation>
  </exportType>

  <functionality>
    <![CDATA[在下方 item 中展示尽可能多的、完备的功能描述。个数不受限]]>
    <title>核心功能介绍</title>
    <features>
      <item>功能描述1</item>
      <item>功能描述2</item>
      <item>功能描述3</item>
      <item>功能描述4</item>
      <item>...个数不受限</item>
    </features>
  </functionality>

  <scenarios>
    <title>使用场景介绍</title>
    <cases>
      <![CDATA[在下方 item 中展示尽可能多的、完备的、业务场景和技术场景使用描述。个数不受限]]>
      <item>使用场景1</item>
      <item>使用场景2</item>
      <item>使用场景3</item>
      <item>使用场景4</item>
      <item>...个数不受限</item>
    </cases>
  </scenarios>


  <![CDATA[仅在导出项为 UI 组件的时候描述 UI 结构]]>
  <uiStructure>
    <title>UI 特征描述</title>
    <styleFeatures>
      <![CDATA[在下方 item 中展示尽可能多的不同纬度下的 UI 特征。个数不受限。比如视图结构，样式风格等]]>
      <item>维度 A 下的 UI 特征描述</item>
      <item>维度 B 下的 UI 特征描述</item>
      <item>维度 C 下的 UI 特征描述</item>
      <item>维度 D 下的 UI 特征描述</item>
      <item>...个数不受限</item>
    </styleFeatures>
  </uiStructure>

  <usage>
    <basicExample>
      <item>
        <ComponentName
          namespace="${context.projectBasicInfo.namespace}"
          {...otherPropsOne}
        />
      </item>
      <item>
        <ComponentName
          namespace="${context.projectBasicInfo.namespace}"
          {...otherPropsTwo}
        />
      </item>
    </basicExample>
  </usage>

  <api>
    <parameters>
      <![CDATA[
      // TypeScript 参数类型定义
      interface ComponentProps {
        // 在这里定义所有参数的类型
        parameterName: string; // 参数说明
        optionalParam?: number; // 可选参数说明
        // 更多参数...
      }
      ]]>
    </parameters>
    <returnValue>
      <type>返回值类型</type>
      <description>返回值说明</description>
    </returnValue>
  </api>

  <considerations>
    <limitations>
      <item>使用限制1</item>
      <item>使用限制2</item>
      <item>...个数不受限</item>
    </limitations>
    <performance>
      <item>性能考虑点1</item>
      <item>性能考虑点2</item>
      <item>...个数不受限</item>
    </performance>
  </considerations>

  <bestPractices>
    <recommendations>
      <item>推荐用法1</item>
      <item>推荐用法2</item>
      <item>...个数不受限</item>
    </recommendations>
    <antiPatterns>
      <item>应避免的用法1</item>
      <item>应避免的用法2</item>
      <item>...个数不受限</item>
    </antiPatterns>
  </bestPractices>

  <decisiveFactors>
    <advantages>
      <item>关键优势1</item>
      <item>关键优势2</item>
      <item>...个数不受限</item>
    </advantages>
    <limitations>
      <item>关键限制1</item>
      <item>关键限制2</item>
      <item>...个数不受限</item>
    </limitations>
  </decisiveFactors>
${this.XML_MARKER_END}

请确保所有内容都基于之前的分析结果，保持客观准确。如果某些信息无法确定，请在 metadata 部分的 missingInfo 中列出。
`

    messages.push({ role: 'user', content: finalPrompt })
    context.projectAnalysisData.conversations.push({
      role: 'user',
      content: finalPrompt,
      timestamp: Date.now(),
    })

    const finalResponse = await this.callModel({
      messages,
      options: {
        ...context.langBridgeOptions,
        retry: {
          maxRetries: 5, // 首次分析使用较少的重试次数
        },
      },
    })
    context.projectAnalysisData.conversations.push({
      role: 'assistant',
      content: finalResponse.data.choices[0].message.content ?? '',
      timestamp: Date.now(),
    })

    return finalResponse.data.choices[0].message.content ?? ''
  }

  private async extractXmlFromResponse(
    response: string,
  ): Promise<AnalysisResponse> {
    try {
      const pattern = new RegExp(
        `${this.XML_MARKER_START}([\\s\\S]*?)${this.XML_MARKER_END}`,
      )
      const match = response.match(pattern)

      if (!match || !match[1]) {
        this.logger.error('未找到 XML 响应标记')
        throw new Error('响应格式错误：未找到 XML 标记')
      }

      const xmlContent = `<root>${match[1].trim()}</root>`

      // 验证 XML 格式
      if (!XMLValidator.validate(xmlContent)) {
        throw new Error('XML 格式无效')
      }

      const parsed = this.xmlParser.parse(xmlContent)
      const root = parsed.root

      // 处理数组项，确保即使只有一个项目也返回数组
      const ensureArray = (value: unknown): unknown[] => {
        if (!value) return []
        return Array.isArray(value) ? value : [value]
      }

      // 提取 filesToAnalyze 数组
      const filesToAnalyze = ensureArray(root.filesToAnalyze?.item || []).map(
        item => (typeof item === 'string' ? item : ''),
      )

      return {
        currentAnalysis: root.currentAnalysis || '',
        requiresAdditionalFiles:
          root.requiresAdditionalFiles === 'true'
          || root.requiresAdditionalFiles === true,
        filesToAnalyze,
        reason: root.reason || '',
        isComplete: root.isComplete === 'true',
        summary: root.summary || '',
      }
    }
    catch (error: unknown) {
      this.logger.error('解析 XML 响应失败:', error)
      throw error instanceof Error ? error : new Error('XML 解析失败')
    }
  }

  private async saveAnalysisResults(
    context: AnalysisContext,
    finalAnalysis: string,
  ): Promise<void> {
    const timestamp = new Date().toISOString()
    const resultsDir = join(
      context.projectAnalysisData.projectDir,
      'analysis_results',
    )

    try {
      await mkdir(resultsDir, { recursive: true })

      // 保存对话记录
      const conversationsPath = join(resultsDir, 'conversations.json')
      await writeFile(
        conversationsPath,
        JSON.stringify(
          {
            timestamp,
            exportName: context.projectBasicInfo.exportName,
            conversations: context.projectAnalysisData.conversations,
          },
          null,
          2,
        ),
      )

      // 保存分析结果
      const analysisPath = join(resultsDir, 'analysis.json')
      await writeFile(
        analysisPath,
        JSON.stringify(
          {
            timestamp,
            exportName: context.projectBasicInfo.exportName,
            analysis: finalAnalysis,
            analyzedFiles: Array.from(
              context.projectAnalysisData.analyzedFiles,
            ),
          },
          null,
          2,
        ),
      )

      // 生成 Markdown 报告
      const markdownPath = join(resultsDir, 'analysis_report.md')
      await writeFile(
        markdownPath,
        this.generateMarkdownReport(context, finalAnalysis, timestamp),
      )

      this.logger.verbose(`分析结果已保存到: ${resultsDir}`)
    }
    catch (error) {
      this.logger.error('保存分析结果失败:', error)
      throw error
    }
  }

  private generateMarkdownReport(
    context: AnalysisContext,
    analysis: string,
    timestamp: string,
  ): string {
    return `# 组件分析报告

## 基本信息
- 仓库地址: ${context.projectBasicInfo.gitUrl}
- 导出项: ${context.projectBasicInfo.exportName}
- 分析时间: ${timestamp}
- 分析的文件数: ${context.projectAnalysisData.analyzedFiles.size}

## 分析结果
${analysis}

## 分析的文件
${Array.from(context.projectAnalysisData.analyzedFiles)
  .map(file => `- ${file}`)
  .join('\n')}

## 对话记录
${context.projectAnalysisData.conversations
  .map(
    (conv, index) => `
### 对话 ${index + 1}
- **角色**: ${conv.role}
- **时间**: ${new Date(conv.timestamp).toISOString()}
${conv.file ? `- **文件**: ${conv.file}` : ''}

\`\`\`text
${conv.content}
\`\`\`
`,
  )
  .join('\n')}
`
  }

  private async getFileContent(
    filePath: string,
    context: AnalysisContext,
  ): Promise<string> {
    if (!filePath) {
      throw new Error('文件路径不能为空')
    }

    if (context.projectAnalysisData.fileContents.has(filePath)) {
      return context.projectAnalysisData.fileContents.get(filePath)!
    }

    try {
      const fullPath = join(context.projectAnalysisData.projectDir, filePath)
      // 检查文件是否存在
      await access(fullPath, fsConstants.F_OK | fsConstants.R_OK)

      const content = await readFile(fullPath, 'utf-8')
      context.projectAnalysisData.fileContents.set(filePath, content)
      this.logger.debug(`读取文件内容: ${filePath}`)
      return content
    }
    catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(`读取文件失败 ${filePath}:`, error)
        throw new Error(`无法读取文件 ${filePath}: ${error.message}`)
      }
      throw new Error(`无法读取文件 ${filePath}`)
    }
  }

  private async isFileExists(
    filePath: string,
    tempDir: string,
  ): Promise<boolean> {
    if (!filePath) return false

    try {
      const fullPath = join(tempDir, filePath)
      await access(fullPath, fsConstants.F_OK)
      return true
    }
    catch {
      return false
    }
  }

  async deleteProjectDir(dir: string): Promise<void> {
    await rm(dir, { recursive: true })
  }
}
