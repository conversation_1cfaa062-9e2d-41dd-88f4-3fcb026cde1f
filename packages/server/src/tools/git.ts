import * as gitUrlParse from 'git-url-parse'
import { GIT_TOKEN } from '@/constants/token'

// 兼容不同的导入方式，处理 CommonJS/ES Module 混合导入
interface GitUrlParseModule {
  default?: typeof gitUrlParse
}

const parseGitUrl: typeof gitUrlParse
  = (gitUrlParse as GitUrlParseModule).default || gitUrlParse

type GitUrl = {
  source: string
  full_name: string
  name: string
  owner: string
  protocol: string
  resource: string
}

export const isValidGitUrl = (git: string): boolean => {
  try {
    parseGitUrl(git)
    return true
  }
  catch (_) {
    return false
  }
}

export const git2httpLink = (git: string): string => {
  const parsed = parseGitUrl(git)
  return `https://git.${parsed.source}/${parsed.full_name}`
}

export const getNamespaceFromGitUrl = (git: string): string => {
  const parsed = parseGitUrl(git)
  return parsed.full_name
}

export const reasonableGitUrlParse = (url: string): GitUrl => {
  try {
    return parseGitUrl(url.trim())
  }
  catch (_) {
    throw new Error(`Invalid git url: ${url}`)
  }
}

export const isSameGitUrl = (git1: string, git2: string): boolean => {
  try {
    return (
      parseGitUrl(git1).full_name === parseGitUrl(git2).full_name
    )
  }
  catch {
    return false
  }
}

/**
 * 将 Git URL 转换为带有 OAuth token 的 HTTPS 格式，用于克隆操作
 * @param gitUrl - 原始的 Git URL（支持 SSH 或 HTTPS 格式）
 * @returns 带有 OAuth token 的 HTTPS Git URL
 */
export const convertToOAuthGitUrl = (gitUrl: string): string => {
  const parsed = parseGitUrl(gitUrl)

  // 构建带有 OAuth token 的 HTTPS URL
  // 格式: https://oauth2:<EMAIL>/namespace/repo.git
  // 确保使用正确的域名格式
  const domain = parsed.source.startsWith('git.') ? parsed.source : `git.${parsed.source}`
  return `https://oauth2:${GIT_TOKEN}@${domain}/${parsed.full_name}.git`
}
