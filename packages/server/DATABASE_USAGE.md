# 数据库使用指南

本项目基于公司的 `@infra-node/sequelize` 包进行数据库操作。以下是详细的使用说明。

## 项目结构

```
src/
├── database/
│   ├── database.service.ts    # 数据库服务
│   ├── database.module.ts     # 数据库模块
│   └── models/
│       ├── index.ts           # 模型管理器
│       └── user.model.ts      # 用户模型示例
└── user/
    ├── user.service.ts        # 用户服务示例
    ├── user.controller.ts     # 用户控制器示例
    └── user.module.ts         # 用户模块
```

## 环境配置

数据库服务会根据 `NODE_ENV` 环境变量自动选择配置：

- `production` → `kconfEnv: 'prod'`
- `staging` → `kconfEnv: 'staging'`
- `test` → `kconfEnv: 'test'`
- `development` → `kconfEnv: 'staging'`（默认）

可以通过环境变量 `DB_CLUSTER_NAME` 自定义集群名称。

## 基本使用

### 1. 在服务中注入数据库服务

```typescript
import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';

@Injectable()
export class YourService {
  constructor(private readonly databaseService: DatabaseService) {}

  async someMethod() {
    // 获取模型管理器
    const models = this.databaseService.getModels();
    
    // 使用模型进行数据库操作
    const users = await models.User.findAll();
    return users;
  }
}
```

### 2. 创建新模型

创建新模型文件 `src/database/models/your-model.model.ts`：

```typescript
import { DataTypes, Model, Sequelize } from '@infra-node/sequelize';

export interface YourModelAttributes {
  id: number;
  name: string;
  // ... 其他字段
}

export interface YourModelCreationAttributes extends Omit<YourModelAttributes, 'id'> {}

export class YourModel extends Model<YourModelAttributes, YourModelCreationAttributes> {
  public id!: number;
  public name!: string;

  static initModel(sequelize: Sequelize): typeof YourModel {
    YourModel.init(
      {
        id: {
          type: DataTypes.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        name: {
          type: DataTypes.STRING(100),
          allowNull: false,
        },
      },
      {
        sequelize,
        modelName: 'YourModel',
        tableName: 'your_table',
        timestamps: true,
        underscored: true,
      }
    );
    return YourModel;
  }
}
```

然后在 `src/database/models/index.ts` 中注册模型：

```typescript
// 添加模型属性
private _YourModel?: typeof YourModel;

// 在 initModels 方法中初始化
initModels(): void {
  this._User = User.initModel(this.sequelize);
  this._YourModel = YourModel.initModel(this.sequelize); // 新增

  this.setupAssociations();
}

// 添加 getter
get YourModel(): typeof YourModel {
  if (!this._YourModel) {
    throw new Error('YourModel not initialized. Call initModels() first.');
  }
  return this._YourModel;
}

// 导出类型
export { YourModel } from './your-model.model';
export type { YourModelAttributes, YourModelCreationAttributes } from './your-model.model';
```

### 3. 常用数据库操作

#### 查询操作

```typescript
// 查找所有记录
const users = await models.User.findAll();

// 根据主键查找
const user = await models.User.findByPk(1);

// 条件查询
const user = await models.User.findOne({
  where: { username: 'john' }
});

// 分页查询
const users = await models.User.findAll({
  limit: 10,
  offset: 0,
  order: [['createdAt', 'DESC']]
});

// 复杂查询
const users = await models.User.findAll({
  where: {
    email: {
      [Op.like]: '%@example.com'
    }
  },
  attributes: ['id', 'username', 'email'],
  include: [/* 关联模型 */]
});
```

#### 创建操作

```typescript
// 创建单条记录
const user = await models.User.create({
  username: 'john',
  email: '<EMAIL>'
});

// 批量创建
const users = await models.User.bulkCreate([
  { username: 'user1', email: '<EMAIL>' },
  { username: 'user2', email: '<EMAIL>' }
]);
```

#### 更新操作

```typescript
// 更新单条记录
const user = await models.User.findByPk(1);
if (user) {
  await user.update({ username: 'newname' });
}

// 批量更新
await models.User.update(
  { username: 'newname' },
  { where: { id: 1 } }
);
```

#### 删除操作

```typescript
// 删除单条记录
const user = await models.User.findByPk(1);
if (user) {
  await user.destroy();
}

// 批量删除
await models.User.destroy({
  where: { id: [1, 2, 3] }
});
```

### 4. 执行原生 SQL

```typescript
// 执行查询
const [results, metadata] = await this.databaseService.query(
  'SELECT * FROM users WHERE created_at > ?',
  {
    replacements: [new Date('2023-01-01')],
    type: QueryTypes.SELECT
  }
);

// 执行更新
await this.databaseService.query(
  'UPDATE users SET status = ? WHERE id = ?',
  {
    replacements: ['active', 1],
    type: QueryTypes.UPDATE
  }
);
```

### 5. 事务处理

```typescript
const sequelize = this.databaseService.getSequelize();
const transaction = await sequelize.transaction();

try {
  const user = await models.User.create({
    username: 'john',
    email: '<EMAIL>'
  }, { transaction });

  // 其他数据库操作...

  await transaction.commit();
} catch (error) {
  await transaction.rollback();
  throw error;
}
```

## API 示例

项目已经创建了完整的用户 CRUD API 示例：

- `POST /users` - 创建用户
- `GET /users` - 获取用户列表（支持分页）
- `GET /users/:id` - 根据 ID 获取用户
- `PUT /users/:id` - 更新用户
- `DELETE /users/:id` - 删除用户
- `POST /users/query` - 执行原生 SQL 查询

## 最佳实践

1. **模型定义**：
   - 使用 TypeScript 接口定义属性类型
   - 为创建操作定义单独的接口（排除自动生成的字段）
   - 添加适当的验证和约束

2. **服务层**：
   - 在服务层处理业务逻辑
   - 使用 try-catch 处理错误
   - 添加适当的日志记录

3. **错误处理**：
   - 捕获并记录数据库错误
   - 向上层抛出有意义的错误信息

4. **性能优化**：
   - 使用索引优化查询性能
   - 避免 N+1 查询问题
   - 合理使用分页

5. **安全性**：
   - 使用参数化查询防止 SQL 注入
   - 验证输入数据
   - 限制查询结果数量

## 启动项目

```bash
# 开发环境
pnpm run dev

# 生产环境
pnpm run build
pnpm run start:prod
```

数据库连接会在应用启动时自动建立，如果连接失败，应用会抛出错误并停止启动。 
