{"name": "@lingzhu-ai-jobs/server", "version": "0.0.1", "private": true, "scripts": {"dev": "pnpm run start:dev", "build": "nest build", "start": "nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "node --max-old-space-size=8192 dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix"}, "dependencies": {"@infra-node/sequelize": "^0.0.5", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@types/semver": "^7.7.0", "axios": "0", "body-parser": "^2.2.0", "chalk": "4", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "dayjs": "^1.11.13", "express": "^5.1.0", "fast-xml-parser": "^5.2.5", "form-data": "^4.0.2", "gen-uniqueid": "^0.0.2", "git-url-parse": "^16.1.0", "lodash": "^4.17.21", "mysql2": "^3.14.1", "nest-winston": "^1.10.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "semver": "^7.7.2", "simple-git": "^3.28.0", "winston": "^3.17.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@stylistic/eslint-plugin": "^4.4.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/body-parser": "^1.19.5", "@types/compression": "^1.8.0", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/lodash": "^4.17.18", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "cross-env": "^7.0.3", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "type-fest": "^4.41.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}}