import {
  BrowserRouter as Router,
  Routes,
  Route,
  Link,
  useLocation,
} from 'react-router-dom'
import { Layout, Menu, Typography } from 'antd'
import { FileTextOutlined, BulbOutlined, BookOutlined } from '@ant-design/icons'
import MaterialSmartDescriptionList from './pages/smart-description/list'
import MaterialSmartDescriptionJobList from './pages/smart-description/jobs'
import ApiDocumentation from './pages/smart-description/docs'

const { Header, Content } = Layout
const { Title } = Typography

const AppContent: React.FC = () => {
  const location = useLocation()

  const getSelectedKey = () => {
    if (location.pathname === '/description') return 'description'
    if (location.pathname === '/job') return 'job'
    return 'docs'
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header
        style={{
          background: '#fff',
          borderBottom: '1px solid #f0f0f0',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            height: '100%',
          }}
        >
          <Title
            level={4}
            style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}
          >
            灵筑 AI 自动化任务
          </Title>
          <Menu
            mode="horizontal"
            selectedKeys={[getSelectedKey()]}
            style={{ borderBottom: 'none', minWidth: 300 }}
            items={[
              {
                key: 'docs',
                icon: <BookOutlined />,
                label: <Link to="/">接入文档</Link>,
              },
              {
                key: 'description',
                icon: <BulbOutlined />,
                label: <Link to="/description">智能描述</Link>,
              },
              {
                key: 'job',
                icon: <FileTextOutlined />,
                label: <Link to="/job">生成任务</Link>,
              },
            ]}
          />
          <div>&nbsp;</div>
        </div>
      </Header>
      <Layout>
        <Content className="page-container">
          <Routes>
            <Route path="/" element={<ApiDocumentation />} />
            <Route
              path="/description"
              element={<MaterialSmartDescriptionList />}
            />
            <Route path="/job" element={<MaterialSmartDescriptionJobList />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  )
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  )
}

export default App
