// 接入文档页面样式
.container {
  .header {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 24px;
    padding: 16px 0 12px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      margin: 0 !important;
      font-size: 16px !important;
      font-weight: 500;
      color: #262626;
      line-height: 1.2;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .apiCard {
    background: #fff;
    border: 1px solid #e8f4fd;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.06);
      transform: translateY(-1px);
    }

    :global(.ant-card-body) {
      padding: 24px;
    }
  }

  .flowCard {
    background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
    border: 1px solid #b7eb8f;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :global(.ant-card-body) {
      padding: 24px;
    }
  }

  .apiHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;

    .apiTitle {
      margin: 0 !important;
      font-size: 16px !important;
      font-weight: 600;
      color: #262626;
    }

    .triggerIcon {
      color: #52c41a;
      font-size: 18px;
    }

    .stageIcon {
      color: #1890ff;
      font-size: 18px;
    }

    .queryIcon {
      color: #722ed1;
      font-size: 18px;
    }
  }

  .apiSection {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    :global(.ant-typography h6) {
      margin-bottom: 12px !important;
      font-size: 14px !important;
      font-weight: 600;
      color: #262626;
    }

    :global(.ant-typography p) {
      margin-bottom: 12px !important;
      color: #595959;
      line-height: 1.6;
    }
  }

  .codeBlock {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin: 12px 0;
    overflow-x: auto;

    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
      color: #24292e;
      white-space: pre;
      word-wrap: normal;
    }
  }

  .flowTitle {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 600;
    color: #262626;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: '🔄';
      font-size: 18px;
    }
  }

  .flowSteps {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .step {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #d9f7be;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.95);
      transform: translateX(4px);
    }
  }

  .stepNumber {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
  }

  .stepContent {
    flex: 1;

    :global(.ant-typography h6) {
      margin-bottom: 8px !important;
      font-size: 14px !important;
      font-weight: 600;
      color: #262626;
    }

    :global(.ant-typography p) {
      margin-bottom: 0 !important;
      color: #595959;
      line-height: 1.6;
    }
  }

  .notesList {
    margin: 0;
    padding-left: 20px;

    li {
      margin-bottom: 12px;
      color: #595959;
      line-height: 1.6;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    .header {
      padding: 12px 0;
      margin-bottom: 20px;

      .title {
        font-size: 15px !important;
      }
    }

    .content {
      gap: 16px;
    }

    .apiCard,
    .flowCard {
      border-radius: 8px;

      :global(.ant-card-body) {
        padding: 16px;
      }
    }

    .apiHeader {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .apiTitle {
        font-size: 15px !important;
      }
    }

    .codeBlock {
      padding: 12px;
      border-radius: 6px;

      pre {
        font-size: 12px;
      }
    }

    .step {
      padding: 12px;
      border-radius: 6px;
    }

    .stepNumber {
      width: 28px;
      height: 28px;
      font-size: 13px;
    }

    .stepContent {
      :global(.ant-typography h6) {
        font-size: 13px !important;
      }

      :global(.ant-typography p) {
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 480px) {
  .container {
    .apiCard,
    .flowCard {
      :global(.ant-card-body) {
        padding: 12px;
      }
    }

    .codeBlock {
      padding: 8px;

      pre {
        font-size: 11px;
      }
    }

    .step {
      padding: 8px;
      gap: 12px;
    }

    .stepNumber {
      width: 24px;
      height: 24px;
      font-size: 12px;
    }
  }
}

// 代码高亮样式
.codeBlock {
  pre {
    // JSON 语法高亮
    .string {
      color: #032f62;
    }

    .number {
      color: #005cc5;
    }

    .boolean {
      color: #d73a49;
    }

    .null {
      color: #6f42c1;
    }

    .key {
      color: #22863a;
    }

    .comment {
      color: #6a737d;
      font-style: italic;
    }
  }
}

// 标签样式优化
:global(.ant-tag) {
  border-radius: 4px;
  font-weight: 500;
  font-size: 12px;
  padding: 2px 8px;
  border: none;

  &.ant-tag-green {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: white;
  }

  &.ant-tag-blue {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
  }

  &.ant-tag-purple {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
    color: white;
  }
}

// 代码文本样式
:global(.ant-typography code) {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #e83e8c;
}
