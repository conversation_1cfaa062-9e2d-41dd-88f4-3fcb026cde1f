import React from 'react'
import { Typography, Card, Divider, Tag, Space } from 'antd'
import {
  ApiOutlined,
  PlayCircleOutlined,
  SearchOutlined,
} from '@ant-design/icons'
import styles from './ApiDocumentation.module.less'

const { Title, Paragraph, Text } = Typography

const ApiDocumentation: React.FC = () => {
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title level={4} className={styles.title}>
          接入文档
        </Title>
        <Text type="secondary">物料智能描述服务 API 接口文档</Text>
      </div>

      <div className={styles.content}>
        {/* 使用流程 */}
        <Card className={styles.flowCard}>
          <Title level={5} className={styles.flowTitle}>
            典型使用流程
          </Title>
          <Divider />
          <div className={styles.flowSteps}>
            <div className={styles.step}>
              <div className={styles.stepNumber}>1</div>
              <div className={styles.stepContent}>
                <Title level={3}>触发生成任务</Title>
                <Paragraph>
                  调用
                  {' '}
                  <Text code>/material-smart-description/job/trigger</Text>
                  {' '}
                  接口， 传入物料标识和调用参数，启动智能描述生成任务。
                </Paragraph>
              </div>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>2</div>
              <div className={styles.stepContent}>
                <Title level={3}>轮询任务状态</Title>
                <Paragraph>
                  定期调用
                  {' '}
                  <Text code>/material-smart-description/job/stage</Text>
                  {' '}
                  接口，
                  查询任务执行状态，直到状态变为 &quot;succeeded&quot; 或 &quot;failed&quot;。
                </Paragraph>
              </div>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>3</div>
              <div className={styles.stepContent}>
                <Title level={3}>获取生成结果</Title>
                <Paragraph>
                  任务成功后，调用
                  {' '}
                  <Text code>/material-smart-description/query</Text>
                  {' '}
                  接口，
                  获取生成的智能描述内容。
                </Paragraph>
              </div>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>4</div>
              <div className={styles.stepContent}>
                <Title level={3}>手动修改描述（可选）</Title>
                <Paragraph>
                  如需调整生成的描述内容，可通过管理界面手动编辑
                  zhidaDescription 字段， 系统会创建新的记录保存修改后的内容。
                </Paragraph>
              </div>
            </div>
          </div>
        </Card>

        {/* 触发接口 */}
        <Card className={styles.apiCard}>
          <div className={styles.apiHeader}>
            <Space>
              <PlayCircleOutlined className={styles.triggerIcon} />
              <Title level={5} className={styles.apiTitle}>
                触发智能描述生成任务
              </Title>
            </Space>
            <Space>
              <Tag color="green">POST</Tag>
              <Text code>/material-smart-description/job/trigger</Text>
            </Space>
          </div>

          <Divider />

          <div className={styles.apiSection}>
            <Title level={3}>接口描述</Title>
            <Paragraph>
              触发指定物料的智能描述生成任务。该接口会创建一个异步任务，通过 AI
              模型为物料生成智能描述内容。
            </Paragraph>
          </div>

          <div className={styles.apiSection}>
            <Title level={3}>请求参数</Title>
            <div className={styles.codeBlock}>
              <pre>
                {`{
  "material": {
    "materialId"?: number,        // 物料 ID（可选）
    "namespace"?: string,         // 物料命名空间（可选）
    "materialVersionName": string // 物料版本名称（必填）
  },
  "options": {
    "invokeLangBridgeBizKey": string,    // 调用语言桥业务标识
    "invokeLangBridgeOperator": string   // 调用语言桥操作者
  }
}

// 示例请求
{
  "material": {
    "materialId": 12345,
    "materialVersionName": "1.0.0"
  },
  "options": {
    "invokeLangBridgeBizKey": "material-platform",
    "invokeLangBridgeOperator": "<EMAIL>"
  }
}`}
              </pre>
            </div>
            <Paragraph type="secondary">
              注意：materialId 和 namespace 至少需要提供一个，用于唯一标识物料。
            </Paragraph>
          </div>

          <div className={styles.apiSection}>
            <Title level={3}>响应结果</Title>
            <div className={styles.codeBlock}>
              <pre>
                {`// 所有接口响应都会被 StandardJSONResponseWrapper 包裹
{
  "code": 0 | 1,           // 0: 失败, 1: 成功
  "message": string,       // 响应消息
  "data": number           // 任务 ID
}

// 示例
{
  "code": 0,
  "message": "success",
  "data": 12345
}`}
              </pre>
            </div>
          </div>
        </Card>

        {/* 查询进度接口 */}
        <Card className={styles.apiCard}>
          <div className={styles.apiHeader}>
            <Space>
              <ApiOutlined className={styles.stageIcon} />
              <Title level={5} className={styles.apiTitle}>
                查询任务进度
              </Title>
            </Space>
            <Space>
              <Tag color="blue">POST</Tag>
              <Text code>/material-smart-description/job/stage</Text>
            </Space>
          </div>

          <Divider />

          <div className={styles.apiSection}>
            <Title level={3}>接口描述</Title>
            <Paragraph>查询指定物料最新的智能描述生成任务进度状态。</Paragraph>
          </div>

          <div className={styles.apiSection}>
            <Title level={3}>请求参数</Title>
            <div className={styles.codeBlock}>
              <pre>
                {`{
  "material": {
    "materialId"?: number,        // 物料 ID（可选）
    "namespace"?: string,         // 物料命名空间（可选）
    "materialVersionName": string // 物料版本名称（必填）
  }
}

// 示例请求
{
  "material": {
    "materialId": 12345,
    "materialVersionName": "1.0.0"
  }
}`}
              </pre>
            </div>
          </div>

          <div className={styles.apiSection}>
            <Title level={3}>响应结果</Title>
            <div className={styles.codeBlock}>
              <pre>
                {`// 所有接口响应都会被 StandardJSONResponseWrapper 包裹
{
  "code": 0 | 1,           // 0: 失败, 1: 成功
  "message": string,       // 响应消息
  "data": {
    "stage": "untriggered" | "pending" | "succeeded" | "failed"
  }
}

// 状态说明：
// - untriggered: 未触发
// - pending: 进行中
// - succeeded: 成功
// - failed: 失败

// 示例
{
  "code": 0,
  "message": "success",
  "data": {
    "stage": "succeeded"
  }
}`}
              </pre>
            </div>
          </div>
        </Card>

        {/* 查询结果接口 */}
        <Card className={styles.apiCard}>
          <div className={styles.apiHeader}>
            <Space>
              <SearchOutlined className={styles.queryIcon} />
              <Title level={5} className={styles.apiTitle}>
                查询智能描述结果
              </Title>
            </Space>
            <Space>
              <Tag color="purple">POST</Tag>
              <Text code>/material-smart-description/query</Text>
            </Space>
          </div>

          <Divider />

          <div className={styles.apiSection}>
            <Title level={3}>接口描述</Title>
            <Paragraph>
              通过物料标识参数查询物料的智能描述结果。返回经过处理的 ZhiDa
              格式描述内容。
            </Paragraph>
          </div>

          <div className={styles.apiSection}>
            <Title level={3}>请求参数</Title>
            <div className={styles.codeBlock}>
              <pre>
                {`{
  "materialId"?: number,        // 物料 ID（可选）
  "namespace"?: string,         // 物料命名空间（可选）
  "materialVersionName": string // 物料版本名称（必填）
}

// 示例请求
{
  "materialId": 12345,
  "materialVersionName": "1.0.0"
}`}
              </pre>
            </div>
            <Paragraph type="secondary">
              注意：materialId 和 namespace 至少需要提供一个，用于唯一标识物料。
            </Paragraph>
          </div>

          <div className={styles.apiSection}>
            <Title level={3}>响应结果</Title>
            <div className={styles.codeBlock}>
              <pre>
                {`// 所有接口响应都会被 StandardJSONResponseWrapper 包裹
{
  "code": 0 | 1,           // 0: 失败, 1: 成功
  "message": string,       // 响应消息
  "data": ZhiDaNeededMaterialDescription | null
}

// ZhiDaNeededMaterialDescription 结构（有数据时）
{
  "namespace": string,     // 物料命名空间
  "title": string,         // 物料中文名称
  "name": string,          // 物料英文名称（组件名称）
  "description": string,   // 组件描述
  "propsDefine": string,   // TypeScript 参数类型定义
  "jsxDemo": string[],     // JSX 示例代码

  // 以下为可选的扩展字段
  "childNested"?: string[],              // 必须附带使用的子组件
  "jsxPropCompatible"?: Record<string, unknown>, // 属性兼容配置
  "purePropEffect"?: Array<{             // 属性效果配置
    "predicate": string,
    "alias": {
      "key": string,
      "value": unknown
    }
  }>,
  "mergePropsBeforeInsert"?: Record<string, unknown> // 插入前合并的属性
}

// 无数据时 data 为 null`}
              </pre>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default ApiDocumentation
