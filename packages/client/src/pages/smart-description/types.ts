/**
 * 智能描述内容的类型定义
 */
export interface SmartDescriptionContent {
  functionality?: {
    features?: string[]
    title?: string
  }
  exportType?: {
    type?: string
    explanation?: string
  }
  scenarios?: {
    title?: string
    cases?: string[]
  }
  uiStructure?: {
    title?: string
    styleFeatures?: string[]
  }
  usage?: {
    basicExample?: string[]
  }
  api?: {
    parameters?: {
      typescriptCode?: string
    }
    returnValue?: {
      type?: string
      description?: string
    }
  }
  considerations?: {
    limitations?: string[]
    performance?: string[]
  }
  bestPractices?: {
    recommendations?: string[]
    antiPatterns?: string[]
  }
  decisiveFactors?: {
    advantages?: string[]
    limitations?: string[]
  }
  [key: string]: unknown
}
