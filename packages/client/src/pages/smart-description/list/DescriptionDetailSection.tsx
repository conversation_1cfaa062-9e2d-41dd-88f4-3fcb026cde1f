import { useState, useCallback, forwardRef, useImperativeHandle } from 'react'
import { Modal, Descriptions, Card, message, Divider, Collapse } from 'antd'
import { materialSmartDescriptionApi } from '../../../services/api'
import { getStateTag } from '@/constants/common'
import { formatTime } from './shared/utils'
import { MODAL_WIDTH } from './shared/constants'
import JsonViewer from './JsonViewer'
import ZhidaDescriptionViewer from './ZhidaDescriptionViewer'
import ZhidaDescriptionEditor from './ZhidaDescriptionEditor'
import type {
  MaterialSmartDescription,
  DescriptionDetailSectionRef,
} from './shared/types'
import type { ZhiDaNeededMaterialDescription } from '../../../services/api'

const DescriptionDetailSection = forwardRef<DescriptionDetailSectionRef>((_, ref) => {
  // 详情弹窗状态
  const [selectedRecord, setSelectedRecord] = useState<MaterialSmartDescription | null>(null)
  const [detailVisible, setDetailVisible] = useState(false)

  // 编辑器状态
  const [editorVisible, setEditorVisible] = useState(false)

  // 查看详情
  const showDetail = useCallback(async (record: MaterialSmartDescription) => {
    try {
      const response = await materialSmartDescriptionApi.getDescriptionById(record.id)
      if (response.data.code === 1) {
        setSelectedRecord(response.data.data)
        setDetailVisible(true)
      }
      else {
        message.error(response.data.message || '获取详情失败')
      }
    }
    catch (error) {
      console.error('获取详情失败:', error)
      message.error('获取详情失败，请稍后重试')
    }
  }, [])

  // 关闭详情弹窗
  const closeDetail = useCallback(() => {
    setDetailVisible(false)
    setSelectedRecord(null)
  }, [])

  // 打开编辑器
  const openEditor = useCallback(() => {
    setEditorVisible(true)
  }, [])

  // 关闭编辑器
  const closeEditor = useCallback(() => {
    setEditorVisible(false)
  }, [])

  // 编辑成功回调
  const handleEditSuccess = useCallback((updatedData: ZhiDaNeededMaterialDescription) => {
    if (selectedRecord) {
      setSelectedRecord({
        ...selectedRecord,
        zhidaDescription: updatedData,
      })
    }
    setEditorVisible(false)
    message.success('保存成功！已创建新版本记录')
  }, [selectedRecord])

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    showDetail,
  }), [showDetail])

  return (
    <Modal
      title={(
        <div style={{
          fontSize: '18px',
          fontWeight: 600,
          color: '#262626',
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: '12px',
          marginBottom: '16px',
        }}
        >
          智能描述详情
        </div>
      )}
      open={detailVisible}
      onCancel={closeDetail}
      footer={null}
      width={MODAL_WIDTH}
      styles={{
        body: { padding: '20px 24px' },
        header: { padding: '16px 24px 0' },
      }}
    >
      {selectedRecord && (
        <div>
          <Descriptions
            column={2}
            style={{ marginBottom: 20 }}
            labelStyle={{
              fontWeight: 600,
              color: '#595959',
              fontSize: '14px',
            }}
            contentStyle={{
              color: '#262626',
              fontSize: '14px',
            }}
          >
            <Descriptions.Item label="物料名称">
              <span style={{ fontWeight: 500, color: '#1890ff' }}>
                {selectedRecord.materialDetail.title}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="组件名称">
              <code style={{
                background: '#e6f7ff',
                color: '#52c41a',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '13px',
                fontWeight: 500,
              }}
              >
                {selectedRecord.materialDetail.name}
              </code>
            </Descriptions.Item>
            <Descriptions.Item label="命名空间">
              <code style={{
                background: '#f6f8fa',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '13px',
              }}
              >
                {selectedRecord.materialDetail.namespace}
              </code>
            </Descriptions.Item>
            <Descriptions.Item label="版本">
              <code style={{
                background: '#f6f8fa',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '13px',
              }}
              >
                {selectedRecord.materialVersion}
              </code>
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {getStateTag(selectedRecord.state)}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间" span={2}>
              <span style={{ color: '#595959', fontSize: '13px' }}>
                {formatTime(selectedRecord.createTime)}
              </span>
            </Descriptions.Item>
          </Descriptions>

          {/* ZhiDa 描述内容 */}
          <Card
            title={(
              <span style={{ fontSize: '16px', fontWeight: 600, color: '#262626' }}>
                ZhiDa 描述内容
              </span>
            )}
            size="small"
            style={{
              borderRadius: '8px',
              border: '1px solid #e8f4fd',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
              marginBottom: '24px',
            }}
            styles={{ body: { padding: '16px' } }}
          >
            <ZhidaDescriptionViewer
              data={selectedRecord.zhidaDescription}
              recordId={selectedRecord.id}
              editable={true}
              onEdit={openEditor}
            />
          </Card>

          {/* 分割线 */}
          <Divider style={{ margin: '24px 0', borderColor: '#f0f0f0' }}>
            <span style={{ color: '#8c8c8c', fontSize: '12px' }}>调试信息</span>
          </Divider>

          {/* 原始智能描述内容 - 默认收起 */}
          <Collapse
            size="small"
            ghost
            items={[
              {
                key: 'debug-raw-description',
                label: (
                  <span style={{ fontSize: '14px', color: '#8c8c8c' }}>
                    原始智能描述内容 (Debug)
                  </span>
                ),
                children: (
                  <Card
                    size="small"
                    style={{
                      borderRadius: '6px',
                      border: '1px solid #f0f0f0',
                      backgroundColor: '#fafafa',
                    }}
                    styles={{ body: { padding: '12px' } }}
                  >
                    <JsonViewer
                      data={selectedRecord.smartDescription}
                      defaultMode="json"
                    />
                  </Card>
                ),
              },
            ]}
          />
        </div>
      )}

      {/* ZhiDa 描述编辑器 */}
      <ZhidaDescriptionEditor
        visible={editorVisible}
        data={selectedRecord?.zhidaDescription || null}
        recordId={selectedRecord?.id || null}
        onCancel={closeEditor}
        onSuccess={handleEditSuccess}
      />
    </Modal>
  )
})

DescriptionDetailSection.displayName = 'DescriptionDetailSection'

export default DescriptionDetailSection
