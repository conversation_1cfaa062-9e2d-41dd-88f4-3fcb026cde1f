import React, { useRef } from 'react'
import { Typography } from 'antd'
import DescriptionListSection from './DescriptionListSection'
import DescriptionDetailSection from './DescriptionDetailSection'
import DescriptionHistorySection from './DescriptionHistorySection'
import type {
  MaterialSmartDescription,
  DescriptionDetailSectionRef,
  DescriptionHistorySectionRef,
} from './shared/types'
import styles from './MaterialSmartDescriptionList.module.less'

const { Title } = Typography

const MaterialSmartDescriptionList: React.FC = () => {
  // 详情和历史功能的引用
  const detailRef = useRef<DescriptionDetailSectionRef>(null)
  const historyRef = useRef<DescriptionHistorySectionRef>(null)

  // 处理查看详情
  const handleViewDetail = (record: MaterialSmartDescription) => {
    detailRef.current?.showDetail(record)
  }

  // 处理查看历史
  const handleViewHistory = (record: MaterialSmartDescription) => {
    historyRef.current?.showHistory(record)
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title level={4} className={styles.title}>
          物料智能描述
        </Title>
      </div>

      {/* 列表区域 */}
      <DescriptionListSection
        onViewDetail={handleViewDetail}
        onViewHistory={handleViewHistory}
      />

      {/* 详情区域 */}
      <DescriptionDetailSection ref={detailRef} />

      {/* 历史区域 */}
      <DescriptionHistorySection ref={historyRef} />
    </div>
  )
}

export default MaterialSmartDescriptionList
