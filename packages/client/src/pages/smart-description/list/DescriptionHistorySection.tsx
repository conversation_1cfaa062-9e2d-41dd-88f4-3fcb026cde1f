import { useState, useCallback, forwardRef, useImperativeHandle } from 'react'
import { Drawer, Spin, Card, Tag, Typography, message } from 'antd'
import { materialSmartDescriptionApi } from '../../../services/api'
import { getStateTag } from '@/constants/common'
import { formatTime, renderSmartDescription } from './shared/utils'
import { DEFAULT_HISTORY_PAGE_SIZE, DRAWER_WIDTH } from './shared/constants'
import type {
  MaterialSmartDescription,
  DescriptionHistorySectionRef
} from './shared/types'

const { Text } = Typography

const DescriptionHistorySection = forwardRef<DescriptionHistorySectionRef>((_, ref) => {
  // 历史版本状态
  const [selectedRecord, setSelectedRecord] = useState<MaterialSmartDescription | null>(null)
  const [historyVisible, setHistoryVisible] = useState(false)
  const [historyData, setHistoryData] = useState<MaterialSmartDescription[]>([])
  const [historyLoading, setHistoryLoading] = useState(false)

  // 获取历史版本数据
  const fetchHistoryData = useCallback(async (materialId: number) => {
    setHistoryLoading(true)
    try {
      const response = await materialSmartDescriptionApi.getHistoryByMaterialId(materialId, {
        pageNum: 1,
        pageSize: DEFAULT_HISTORY_PAGE_SIZE,
      })
      if (response.data.code === 1) {
        setHistoryData(response.data.data.list)
      }
      else {
        message.error(response.data.message || '获取历史数据失败')
      }
    }
    catch (error) {
      console.error('获取历史数据失败:', error)
      message.error('获取历史数据失败，请稍后重试')
    }
    finally {
      setHistoryLoading(false)
    }
  }, [])

  // 查看历史版本
  const showHistory = useCallback((record: MaterialSmartDescription) => {
    setSelectedRecord(record)
    fetchHistoryData(record.materialId)
    setHistoryVisible(true)
  }, [fetchHistoryData])

  // 关闭历史版本抽屉
  const closeHistory = useCallback(() => {
    setHistoryVisible(false)
    setSelectedRecord(null)
    setHistoryData([])
  }, [])

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    showHistory,
  }), [showHistory])

  return (
    <Drawer
      title={
        <div style={{
          fontSize: '18px',
          fontWeight: 600,
          color: '#262626',
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: '12px',
          marginBottom: '16px'
        }}>
          历史版本 - {selectedRecord?.materialDetail.title}
          <span style={{
            fontSize: '14px',
            fontWeight: 400,
            color: '#8c8c8c',
            marginLeft: '8px'
          }}>
            ({selectedRecord?.materialDetail.name})
          </span>
        </div>
      }
      open={historyVisible}
      onClose={closeHistory}
      width={DRAWER_WIDTH}
      styles={{
        body: { padding: '20px' },
        header: { padding: '16px 24px 0' }
      }}
    >
      <Spin spinning={historyLoading}>
        {historyData.length > 0
          ? (
            <div>
              {historyData.map((item, index) => (
                <Card
                  key={item.id}
                  size="small"
                  style={{
                    marginBottom: 16,
                    borderRadius: '8px',
                    border: '1px solid #e8f4fd',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                    transition: 'all 0.2s ease'
                  }}
                  styles={{ body: { padding: '16px' } }}
                  title={(
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      fontSize: '14px',
                      fontWeight: 600
                    }}>
                      <span style={{ color: '#262626' }}>
                        版本 {item.materialVersion}
                      </span>
                      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                        {index === 0 && (
                          <Tag
                            color="green"
                            style={{
                              borderRadius: '4px',
                              fontSize: '12px',
                              fontWeight: 500
                            }}
                          >
                            最新
                          </Tag>
                        )}
                        {getStateTag(item.state)}
                      </div>
                    </div>
                  )}
                >
                  <div style={{ marginBottom: 12 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>创建时间：</Text>
                    <Text style={{ fontSize: '13px', color: '#595959' }}>
                      {formatTime(item.createTime)}
                    </Text>
                  </div>
                  <div style={{
                    background: '#fafbfc',
                    padding: '12px',
                    borderRadius: '6px',
                    border: '1px solid #e1e4e8'
                  }}>
                    {renderSmartDescription(item.smartDescription)}
                  </div>
                </Card>
              ))}
            </div>
          )
          : (
            <div style={{
              textAlign: 'center',
              padding: '60px 40px',
              background: '#fafbfc',
              borderRadius: '8px',
              border: '1px dashed #d0d7de'
            }}>
              <Text type="secondary" style={{ fontSize: '14px' }}>
                暂无历史版本
              </Text>
            </div>
          )}
      </Spin>
    </Drawer>
  )
})

DescriptionHistorySection.displayName = 'DescriptionHistorySection'

export default DescriptionHistorySection
