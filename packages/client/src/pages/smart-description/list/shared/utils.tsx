import React from 'react'
import { Typography, Tag, Space } from 'antd'
import dayjs from 'dayjs'
import JsonViewer from '../JsonViewer'
import type { SmartDescriptionContent } from '../../types'
import type { ZhiDaNeededMaterialDescription } from '../../../../services/api'

const { Text, Paragraph } = Typography

/**
 * 格式化时间戳
 */
export const formatTime = (timestamp: number): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 渲染智能描述内容
 */
export const renderSmartDescription = (
  description: unknown,
): React.ReactElement => {
  if (!description) return <Text type="secondary">暂无描述</Text>

  // 自定义的格式化渲染函数（用于表格简化显示）
  const customRenderFormatted = (content: SmartDescriptionContent) => (
    <div>
      {content.functionality?.features && (
        <div style={{ marginBottom: 16 }}>
          <Text strong>功能特性：</Text>
          <ul style={{ marginTop: 4, marginBottom: 0 }}>
            {content.functionality.features
              .slice(0, 3)
              .map((feature: string, index: number) => (
                <li key={index}>{feature}</li>
              ))}
            {content.functionality.features.length > 3 && <li>...</li>}
          </ul>
        </div>
      )}
      {content.exportType && (
        <div>
          <Text strong>导出类型：</Text>
          <Text code>{content.exportType.type}</Text>
          {content.exportType.explanation && (
            <Paragraph
              type="secondary"
              style={{ marginTop: 4, marginBottom: 0 }}
            >
              {content.exportType.explanation}
            </Paragraph>
          )}
        </div>
      )}
    </div>
  )

  return (
    <JsonViewer
      data={description}
      defaultMode="formatted"
      renderFormatted={customRenderFormatted}
    />
  )
}

/**
 * 渲染 ZhiDa 描述内容（列表简化版）
 */
export const renderZhidaDescription = (
  zhidaDescription: ZhiDaNeededMaterialDescription,
): React.ReactElement => {
  if (!zhidaDescription) return <Text type="secondary">暂无 ZhiDa 描述</Text>

  return (
    <div style={{ maxWidth: '300px' }}>
      {/* 功能描述 */}
      {zhidaDescription.description && (
        <div style={{ marginBottom: 8 }}>
          <Text strong style={{ fontSize: '12px', color: '#595959' }}>功能描述：</Text>
          <Paragraph
            ellipsis={{ rows: 2, tooltip: zhidaDescription.description }}
            style={{
              margin: '4px 0 0 0',
              fontSize: '12px',
              color: '#262626',
              lineHeight: '1.4'
            }}
          >
            {zhidaDescription.description}
          </Paragraph>
        </div>
      )}

      {/* JSX 示例数量 */}
      {zhidaDescription.jsxDemo && zhidaDescription.jsxDemo.length > 0 && (
        <div style={{ marginBottom: 8 }}>
          <Space size={4}>
            <Text style={{ fontSize: '12px', color: '#595959' }}>示例：</Text>
            <Tag color="blue" style={{ fontSize: '11px', padding: '0 4px' }}>
              {zhidaDescription.jsxDemo.length} 个
            </Tag>
          </Space>
        </div>
      )}

      {/* 属性定义状态 */}
      {zhidaDescription.propsDefine && (
        <div>
          <Space size={4}>
            <Text style={{ fontSize: '12px', color: '#595959' }}>属性：</Text>
            <Tag color="green" style={{ fontSize: '11px', padding: '0 4px' }}>
              已定义
            </Tag>
          </Space>
        </div>
      )}
    </div>
  )
}
