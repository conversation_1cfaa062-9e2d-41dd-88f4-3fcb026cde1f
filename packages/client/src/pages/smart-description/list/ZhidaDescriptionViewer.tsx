import React, { useState } from 'react'
import {
  Card,
  Descriptions,
  Typography,
  Divider,
  Space,
  Tag,
  Button,
} from 'antd'
import {
  CodeOutlined,
  FileTextOutlined,
  ApiOutlined,
  EditOutlined,
} from '@ant-design/icons'
import JsonView from '@uiw/react-json-view'
import type { ZhiDaNeededMaterialDescription } from '../../../services/api'

const { Text, Paragraph } = Typography

interface ZhidaDescriptionViewerProps {
  data: ZhiDaNeededMaterialDescription
  recordId?: number
  editable?: boolean
  onEdit?: () => void
}

/**
 * ZhiDa 描述内容展示组件
 * 专门用于展示 zhidaDescription 字段的格式化内容
 */
const ZhidaDescriptionViewer: React.FC<ZhidaDescriptionViewerProps> = ({
  data,
  editable = false,
  onEdit,
}) => {
  const [viewMode, setViewMode] = useState<'formatted' | 'json'>('formatted')

  if (!data) {
    return <Text type="secondary">暂无 ZhiDa 描述数据</Text>
  }

  // 格式化视图内容
  const renderFormattedView = () => (
    <div>
      {/* 基础信息 */}
      <Descriptions
        column={2}
        size="small"
        style={{ marginBottom: 16 }}
        labelStyle={{
          fontWeight: 600,
          color: '#595959',
          fontSize: '13px',
          width: '100px',
        }}
        contentStyle={{
          color: '#262626',
          fontSize: '13px',
        }}
      >
        <Descriptions.Item label="物料名称">
          <Text strong style={{ color: '#1890ff' }}>
            {data.title}
          </Text>
        </Descriptions.Item>
        <Descriptions.Item label="组件名称">
          <Text
            code
            style={{
              background: '#e6f7ff',
              color: '#52c41a',
              padding: '2px 6px',
              borderRadius: '4px',
              fontSize: '12px',
              fontWeight: 500,
            }}
          >
            {data.name}
          </Text>
        </Descriptions.Item>
        <Descriptions.Item label="命名空间">
          <Text
            code
            style={{
              background: '#f6f8fa',
              padding: '2px 6px',
              borderRadius: '4px',
              fontSize: '12px',
            }}
          >
            {data.namespace}
          </Text>
        </Descriptions.Item>
      </Descriptions>

      {/* 功能描述 */}
      {data.description && (
        <Card
          size="small"
          style={{ marginBottom: 16 }}
          title={(
            <Space>
              <FileTextOutlined style={{ color: '#52c41a' }} />
              <span style={{ fontSize: '14px', fontWeight: 600 }}>
                功能描述
              </span>
            </Space>
          )}
          styles={{ body: { padding: '12px 16px' } }}
        >
          <Paragraph
            style={{
              margin: 0,
              fontSize: '13px',
              lineHeight: '1.6',
              color: '#595959',
            }}
          >
            {data.description}
          </Paragraph>
        </Card>
      )}

      {/* 属性定义 */}
      {data.propsDefine && (
        <Card
          size="small"
          style={{ marginBottom: 16 }}
          title={(
            <Space>
              <ApiOutlined style={{ color: '#1890ff' }} />
              <span style={{ fontSize: '14px', fontWeight: 600 }}>
                属性定义
              </span>
            </Space>
          )}
          styles={{ body: { padding: '12px 16px' } }}
        >
          <pre
            style={{
              background: '#f6f8fa',
              padding: '12px',
              borderRadius: '6px',
              fontSize: '12px',
              lineHeight: '1.5',
              margin: 0,
              overflow: 'auto',
              border: '1px solid #e1e4e8',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            }}
          >
            <code>{data.propsDefine}</code>
          </pre>
        </Card>
      )}

      {/* JSX 示例 */}
      {data.jsxDemo && data.jsxDemo.length > 0 && (
        <Card
          size="small"
          style={{ marginBottom: 16 }}
          title={(
            <Space>
              <CodeOutlined style={{ color: '#fa8c16' }} />
              <span style={{ fontSize: '14px', fontWeight: 600 }}>
                使用示例
              </span>
            </Space>
          )}
          styles={{ body: { padding: '12px 16px' } }}
        >
          {data.jsxDemo.map((example, index) => (
            <div
              key={index}
              style={{ marginBottom: index < data.jsxDemo.length - 1 ? 12 : 0 }}
            >
              {data.jsxDemo.length > 1 && (
                <Text
                  type="secondary"
                  style={{
                    fontSize: '12px',
                    marginBottom: '6px',
                    display: 'block',
                  }}
                >
                  示例
                  {' '}
                  {index + 1}
                  :
                </Text>
              )}
              <pre
                style={{
                  background: '#f6f8fa',
                  padding: '12px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  lineHeight: '1.5',
                  margin: 0,
                  overflow: 'auto',
                  border: '1px solid #e1e4e8',
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                }}
              >
                <code>{example}</code>
              </pre>
            </div>
          ))}
        </Card>
      )}

      {/* 扩展字段 */}
      {(data.childNested?.length
        || data.jsxPropCompatible
        || data.mergePropsBeforeInsert
        || data.purePropEffect !== undefined) && (
        <>
          <Divider style={{ margin: '16px 0' }} />
          <Card
            size="small"
            title={(
              <span
                style={{ fontSize: '14px', fontWeight: 600, color: '#595959' }}
              >
                扩展配置
              </span>
            )}
            styles={{ body: { padding: '12px 16px' } }}
          >
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {data.childNested && data.childNested.length > 0 && (
                <div>
                  <Text strong style={{ fontSize: '12px', color: '#595959' }}>
                    子组件嵌套
                    <Text
                      type="secondary"
                      style={{
                        fontSize: '11px',
                        fontWeight: 'normal',
                        marginLeft: '4px',
                      }}
                    >
                      (childNested)
                    </Text>
                    :
                  </Text>
                  <div style={{ marginTop: 4 }}>
                    {data.childNested.map((item, index) => (
                      <Tag
                        key={index}
                        style={{ fontSize: '11px', margin: '2px' }}
                      >
                        {item}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}

              {data.jsxPropCompatible && (
                <div>
                  <Text strong style={{ fontSize: '12px', color: '#595959' }}>
                    JSX 属性兼容
                    <Text
                      type="secondary"
                      style={{
                        fontSize: '11px',
                        fontWeight: 'normal',
                        marginLeft: '4px',
                      }}
                    >
                      (jsxPropCompatible)
                    </Text>
                    :
                  </Text>
                  <pre
                    style={{
                      background: '#fafafa',
                      padding: '8px',
                      borderRadius: '4px',
                      fontSize: '11px',
                      margin: '4px 0 0 0',
                      border: '1px solid #f0f0f0',
                    }}
                  >
                    {JSON.stringify(data.jsxPropCompatible, null, 2)}
                  </pre>
                </div>
              )}

              {data.mergePropsBeforeInsert && (
                <div>
                  <Text strong style={{ fontSize: '12px', color: '#595959' }}>
                    插入前属性合并
                    <Text
                      type="secondary"
                      style={{
                        fontSize: '11px',
                        fontWeight: 'normal',
                        marginLeft: '4px',
                      }}
                    >
                      (mergePropsBeforeInsert)
                    </Text>
                    :
                  </Text>
                  <pre
                    style={{
                      background: '#fafafa',
                      padding: '8px',
                      borderRadius: '4px',
                      fontSize: '11px',
                      margin: '4px 0 0 0',
                      border: '1px solid #f0f0f0',
                    }}
                  >
                    {JSON.stringify(data.mergePropsBeforeInsert, null, 2)}
                  </pre>
                </div>
              )}

              {data.purePropEffect !== undefined && (
                <div>
                  <Text strong style={{ fontSize: '12px', color: '#595959' }}>
                    属性二次适配
                    <Text
                      type="secondary"
                      style={{
                        fontSize: '11px',
                        fontWeight: 'normal',
                        marginLeft: '4px',
                      }}
                    >
                      (purePropEffect)
                    </Text>
                    :
                  </Text>
                  <pre
                    style={{
                      background: '#fafafa',
                      padding: '8px',
                      borderRadius: '4px',
                      fontSize: '11px',
                      margin: '4px 0 0 0',
                      border: '1px solid #f0f0f0',
                    }}
                  >
                    {JSON.stringify(data.purePropEffect, null, 2)}
                  </pre>
                </div>
              )}
            </Space>
          </Card>
        </>
      )}
    </div>
  )

  // JSON 视图内容
  const renderJsonView = () => (
    <JsonView
      value={data}
      style={{
        backgroundColor: '#fafafa',
        padding: '12px',
        borderRadius: '6px',
        fontSize: '13px',
        border: '1px solid #d9d9d9',
        maxHeight: '500px',
        overflow: 'auto',
      }}
      displayDataTypes={false}
      displayObjectSize={false}
      enableClipboard={true}
      collapsed={1}
    />
  )

  return (
    <div>
      {/* 视图模式切换和编辑按钮 */}
      <div
        style={{
          marginBottom: 16,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Space>
          <Button
            size="small"
            type={viewMode === 'formatted' ? 'primary' : 'default'}
            onClick={() => setViewMode('formatted')}
          >
            格式化视图
          </Button>
          <Button
            size="small"
            type={viewMode === 'json' ? 'primary' : 'default'}
            onClick={() => setViewMode('json')}
          >
            JSON 视图
          </Button>
        </Space>

        {editable && onEdit && (
          <Button
            size="small"
            type="primary"
            ghost
            icon={<EditOutlined />}
            onClick={onEdit}
          >
            编辑
          </Button>
        )}
      </div>

      {/* 内容展示 */}
      {viewMode === 'formatted' ? renderFormattedView() : renderJsonView()}
    </div>
  )
}

export default ZhidaDescriptionViewer
