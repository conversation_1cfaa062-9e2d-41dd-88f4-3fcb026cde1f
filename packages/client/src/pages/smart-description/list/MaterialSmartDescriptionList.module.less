// 物料智能描述列表页面样式
.container {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 0 12px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      margin: 0 !important;
      font-size: 16px !important;
      font-weight: 500;
      color: #262626;
      line-height: 1.2;
    }

    .actions {
      display: flex;
      gap: 12px;

      :global(.ant-btn) {
        height: 36px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .searchSection {
    background: #fff;
    border: 1px solid #e8f4fd;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :global(.ant-alert) {
      border-radius: 8px;
      border: 1px solid #e6f7ff;
      background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);

      .ant-alert-icon {
        color: #1890ff;
      }

      .ant-alert-message {
        font-weight: 600;
        color: #262626;
      }

      .ant-alert-description {
        color: #595959;
        line-height: 1.6;
      }
    }
  }

  .tableContainer {
    background: #fff;
    border: 1px solid #e8f4fd;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);

    :global(.ant-table) {
      .ant-table-thead > tr > th {
        background: linear-gradient(135deg, #fafbff 0%, #f5f7fa 100%);
        border-bottom: 2px solid #e8f4fd;
        font-weight: 600;
        color: #262626;
        font-size: 14px;
        padding: 16px 12px;

        &:first-child {
          border-top-left-radius: 8px;
        }

        &:last-child {
          border-top-right-radius: 8px;
        }
      }

      .ant-table-tbody > tr {
        transition: all 0.2s ease;

        &:hover {
          background: #f8faff !important;
          transform: scale(1.001);
        }

        > td {
          padding: 16px 12px;
          border-bottom: 1px solid #f5f5f5;
          vertical-align: top;
        }
      }

      .ant-table-row:last-child > td {
        border-bottom: none;
      }

      // 紧凑行样式
      .compact-row > td {
        padding: 8px 12px !important;
        line-height: 1.3 !important;
      }
    }

    :global(.ant-pagination) {
      margin-top: 24px;
      text-align: right;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;

      .ant-pagination-item {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        transition: all 0.2s ease;

        &:hover {
          border-color: #40a9ff;
          transform: translateY(-1px);
        }

        &.ant-pagination-item-active {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          border-color: #1890ff;
        }
      }

      .ant-pagination-prev,
      .ant-pagination-next {
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    .header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
      padding: 12px 0;
      margin-bottom: 20px;

      .title {
        font-size: 15px !important;
      }

      .actions {
        width: 100%;
        justify-content: flex-start;

        :global(.ant-btn) {
          height: 32px;
          font-size: 14px;
        }
      }
    }

    .searchSection,
    .tableContainer {
      padding: 16px;
      margin-bottom: 16px;
      border-radius: 8px;
    }

    .tableContainer {
      :global(.ant-table) {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 12px 8px;
          font-size: 13px;
        }
      }

      :global(.ant-pagination) {
        margin-top: 16px;
        text-align: center;

        .ant-pagination-options {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .container {
    .tableContainer {
      padding: 12px;

      :global(.ant-table) {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 6px;
          font-size: 12px;
        }
      }
    }
  }
}
