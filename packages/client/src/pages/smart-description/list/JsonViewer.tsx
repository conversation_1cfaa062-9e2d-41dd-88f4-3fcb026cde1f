import React from 'react'
import { Button, Space, Typography } from 'antd'
import JsonView from '@uiw/react-json-view'
import type { SmartDescriptionContent } from '../types'

const { Text } = Typography

interface JsonViewerProps {
  data: unknown
  defaultMode?: 'formatted' | 'json'
  renderFormatted?: (data: SmartDescriptionContent) => React.ReactElement
}

/**
 * JSON 数据展示组件
 * 支持格式化视图和原始 JSON 视图切换
 */
const JsonViewer: React.FC<JsonViewerProps> = ({
  data,
  defaultMode = 'formatted',
  renderFormatted,
}) => {
  const [viewMode, setViewMode] = React.useState<'formatted' | 'json'>(defaultMode)

  if (!data) {
    return <Text type="secondary">暂无数据</Text>
  }

  // 解析数据
  let parsedData: SmartDescriptionContent
  try {
    parsedData = typeof data === 'string' ? JSON.parse(data) : data as SmartDescriptionContent
  }
  catch (_error) {
    return <Text type="secondary">数据格式错误</Text>
  }

  // 默认的格式化渲染函数
  const defaultRenderFormatted = (content: SmartDescriptionContent) => (
    <div>
      {/* 功能模块 */}
      {content.functionality?.features && (
        <div style={{ marginBottom: 16 }}>
          <Text strong>功能特性：</Text>
          <ul style={{ marginTop: 4, marginBottom: 0 }}>
            {content.functionality.features
              .slice(0, 5)
              .map((feature: string, index: number) => (
                <li key={index}>{feature}</li>
              ))}
            {content.functionality.features.length > 5 && (
              <li>
                ... 还有
                {content.functionality.features.length - 5}
                {' '}
                项
              </li>
            )}
          </ul>
        </div>
      )}

      {/* 导出类型 */}
      {content.exportType && (
        <div style={{ marginBottom: 16 }}>
          <Text strong>导出类型：</Text>
          <Text code>{content.exportType.type}</Text>
          {content.exportType.explanation && (
            <Typography.Paragraph
              type="secondary"
              style={{ marginTop: 4, marginBottom: 0 }}
            >
              {content.exportType.explanation}
            </Typography.Paragraph>
          )}
        </div>
      )}

      {/* 使用场景 */}
      {content.scenarios?.cases && (
        <div style={{ marginBottom: 16 }}>
          <Text strong>使用场景：</Text>
          <ul style={{ marginTop: 4, marginBottom: 0 }}>
            {content.scenarios.cases
              .slice(0, 3)
              .map((scenario: string, index: number) => (
                <li key={index}>{scenario}</li>
              ))}
            {content.scenarios.cases.length > 3 && (
              <li>
                ... 还有
                {content.scenarios.cases.length - 3}
                {' '}
                项
              </li>
            )}
          </ul>
        </div>
      )}

      {/* UI 结构特征 */}
      {content.uiStructure?.styleFeatures && (
        <div style={{ marginBottom: 16 }}>
          <Text strong>UI 特征：</Text>
          <ul style={{ marginTop: 4, marginBottom: 0 }}>
            {content.uiStructure.styleFeatures
              .slice(0, 3)
              .map((feature: string, index: number) => (
                <li key={index}>{feature}</li>
              ))}
            {content.uiStructure.styleFeatures.length > 3 && (
              <li>
                ... 还有
                {content.uiStructure.styleFeatures.length - 3}
                {' '}
                项
              </li>
            )}
          </ul>
        </div>
      )}

      {/* API 参数 */}
      {content.api?.parameters?.typescriptCode && (
        <div style={{ marginBottom: 16 }}>
          <Text strong>API 参数：</Text>
          <pre
            style={{
              marginTop: 4,
              marginBottom: 0,
              background: '#f6f8fa',
              padding: '12px',
              borderRadius: '6px',
              fontSize: '13px',
              border: '1px solid #e1e4e8',
              overflow: 'auto',
              maxHeight: '200px',
            }}
          >
            <code>{content.api.parameters.typescriptCode}</code>
          </pre>
        </div>
      )}

      {/* 返回值 */}
      {content.api?.returnValue && (content.api.returnValue.type || content.api.returnValue.description) && (
        <div style={{ marginBottom: 16 }}>
          <Text strong>返回值：</Text>
          <div style={{ marginTop: 4 }}>
            {content.api.returnValue.type && (
              <div>
                <Text type="secondary">类型：</Text>
                <Text code>{content.api.returnValue.type}</Text>
              </div>
            )}
            {content.api.returnValue.description && (
              <div style={{ marginTop: 4 }}>
                <Text type="secondary">说明：</Text>
                <Text>{content.api.returnValue.description}</Text>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )

  return (
    <div>
      {/* 视图模式切换 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            size="small"
            type={viewMode === 'formatted' ? 'primary' : 'default'}
            onClick={() => setViewMode('formatted')}
          >
            格式化视图
          </Button>
          <Button
            size="small"
            type={viewMode === 'json' ? 'primary' : 'default'}
            onClick={() => setViewMode('json')}
          >
            JSON 视图
          </Button>
        </Space>
      </div>

      {/* 内容展示 */}
      {viewMode === 'formatted'
        ? (
          renderFormatted ? renderFormatted(parsedData) : defaultRenderFormatted(parsedData)
        )
        : (
          <JsonView
            value={parsedData}
            style={{
              backgroundColor: '#fafafa',
              padding: '12px',
              borderRadius: '6px',
              fontSize: '13px',
              border: '1px solid #d9d9d9',
              maxHeight: '500px',
              overflow: 'auto',
            }}
            displayDataTypes={false}
            displayObjectSize={false}
            enableClipboard={true}
            collapsed={1}
          />
        )}
    </div>
  )
}

export default JsonViewer
