import { useState, useCallback, forwardRef, useImperativeHandle } from 'react'
import { Modal, Form, Input, Row, Col, Alert, message } from 'antd'
import { materialSmartDescriptionJobApi } from '../../../services/api'
import { TRIGGER_MODAL_WIDTH } from './shared/constants'
import type {
  TriggerJobParams,
  JobTriggerSectionRef,
  JobTriggerSectionProps
} from './shared/types'

const JobTriggerSection = forwardRef<JobTriggerSectionRef, JobTriggerSectionProps>(({
  onSuccess,
}, ref) => {
  // 触发任务状态
  const [triggerVisible, setTriggerVisible] = useState(false)
  const [triggerLoading, setTriggerLoading] = useState(false)
  const [form] = Form.useForm()

  // 触发新任务
  const handleTriggerJob = useCallback(async (values: TriggerJobParams) => {
    setTriggerLoading(true)
    try {
      const response = await materialSmartDescriptionJobApi.triggerJob({
        material: {
          materialId: values.materialId ? parseInt(values.materialId) : undefined,
          namespace: values.namespace,
          materialVersionName: values.materialVersionName,
        },
        options: {
          invokeLangBridgeBizKey: values.invokeLangBridgeBizKey,
          invokeLangBridgeOperator: values.invokeLangBridgeOperator,
        },
      })
      if (response.data.code === 1) {
        message.success(`任务创建成功，任务ID: ${response.data.data}`)
        setTriggerVisible(false)
        form.resetFields()
        onSuccess()
        return true
      } else {
        message.error(response.data.message || '任务创建失败')
        return false
      }
    } catch (error) {
      console.error('任务创建失败:', error)
      message.error('任务创建失败，请稍后重试')
      return false
    } finally {
      setTriggerLoading(false)
    }
  }, [form, onSuccess])

  // 打开触发任务弹窗
  const showTrigger = useCallback(() => {
    setTriggerVisible(true)
  }, [])

  // 关闭触发任务弹窗
  const closeTrigger = useCallback(() => {
    setTriggerVisible(false)
    form.resetFields()
  }, [form])

  // 处理表单提交
  const handleOk = () => {
    form.submit()
  }

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    showTrigger,
  }), [showTrigger])

  return (
    <Modal
      title={
        <div style={{
          fontSize: '18px',
          fontWeight: 600,
          color: '#262626',
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: '12px',
          marginBottom: '16px'
        }}>
          创建智能描述任务
        </div>
      }
      open={triggerVisible}
      onCancel={closeTrigger}
      onOk={handleOk}
      confirmLoading={triggerLoading}
      width={TRIGGER_MODAL_WIDTH}
      styles={{
        body: { padding: '20px 24px' },
        header: { padding: '16px 24px 0' }
      }}
      okButtonProps={{
        style: {
          borderRadius: '6px',
          fontWeight: 500,
          height: '36px'
        }
      }}
      cancelButtonProps={{
        style: {
          borderRadius: '6px',
          fontWeight: 500,
          height: '36px'
        }
      }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleTriggerJob}
        style={{ marginTop: '8px' }}
      >
        <Alert
          message="物料标识"
          description="请提供物料ID或命名空间来标识物料，两者至少提供一个。"
          type="info"
          showIcon
          closable={false}
          style={{
            marginBottom: 20,
            borderRadius: '8px',
            border: '1px solid #e6f7ff',
            background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)'
          }}
        />

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={<span style={{ fontWeight: 600, color: '#262626' }}>物料ID</span>}
              name="materialId"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value && !getFieldValue('namespace')) {
                      return Promise.reject('物料ID和命名空间至少填写一个')
                    }
                    return Promise.resolve()
                  },
                }),
              ]}
            >
              <Input
                placeholder="请输入物料ID"
                style={{
                  borderRadius: '6px',
                  height: '36px'
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={<span style={{ fontWeight: 600, color: '#262626' }}>命名空间</span>}
              name="namespace"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value && !getFieldValue('materialId')) {
                      return Promise.reject('物料ID和命名空间至少填写一个')
                    }
                    return Promise.resolve()
                  },
                }),
              ]}
            >
              <Input
                placeholder="请输入命名空间"
                style={{
                  borderRadius: '6px',
                  height: '36px'
                }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label={<span style={{ fontWeight: 600, color: '#262626' }}>物料版本名称</span>}
          name="materialVersionName"
          rules={[{ required: true, message: '请输入物料版本名称' }]}
        >
          <Input
            placeholder="请输入物料版本名称"
            style={{
              borderRadius: '6px',
              height: '36px'
            }}
          />
        </Form.Item>

        <Form.Item
          label={<span style={{ fontWeight: 600, color: '#262626' }}>调用业务标识</span>}
          name="invokeLangBridgeBizKey"
          rules={[{ required: true, message: '请输入调用业务标识' }]}
        >
          <Input
            placeholder="请输入调用业务标识"
            style={{
              borderRadius: '6px',
              height: '36px'
            }}
          />
        </Form.Item>

        <Form.Item
          label={<span style={{ fontWeight: 600, color: '#262626' }}>操作员</span>}
          name="invokeLangBridgeOperator"
          rules={[{ required: true, message: '请输入操作员' }]}
        >
          <Input
            placeholder="请输入操作员"
            style={{
              borderRadius: '6px',
              height: '36px'
            }}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
})

JobTriggerSection.displayName = 'JobTriggerSection'

export default JobTriggerSection
