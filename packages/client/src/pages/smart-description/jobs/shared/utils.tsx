import React from 'react'
import { Tag } from 'antd'
import dayjs from 'dayjs'
import { JOB_STAGING_LABELS } from '../../../../constants/job'

/**
 * 格式化时间戳
 */
export const formatTime = (timestamp: number): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取任务阶段标签
 */
export const getStagingTag = (staging: string): React.ReactElement => {
  const config = JOB_STAGING_LABELS[staging as keyof typeof JOB_STAGING_LABELS]
  if (!config) {
    return <Tag>未知</Tag>
  }
  return <Tag color={config.color}>{config.text}</Tag>
}
