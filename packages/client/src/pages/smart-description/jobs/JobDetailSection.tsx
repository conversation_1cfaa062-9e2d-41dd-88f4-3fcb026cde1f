import { useState, useCallback, forwardRef, useImperativeHandle } from 'react'
import { Modal, Descriptions, Card, Button, Space, Typography } from 'antd'
import { FileTextOutlined } from '@ant-design/icons'
import { getStateTag } from '@/constants/common'
import { formatTime, getStagingTag } from './shared/utils'
import { DETAIL_MODAL_WIDTH } from './shared/constants'
import type {
  MaterialSmartDescriptionJob,
  JobDetailSectionRef
} from './shared/types'

const { Text } = Typography

const JobDetailSection = forwardRef<JobDetailSectionRef>((_, ref) => {
  // 详情弹窗状态
  const [selectedRecord, setSelectedRecord] = useState<MaterialSmartDescriptionJob | null>(null)
  const [detailVisible, setDetailVisible] = useState(false)

  // 查看详情
  const showDetail = useCallback((record: MaterialSmartDescriptionJob) => {
    setSelectedRecord(record)
    setDetailVisible(true)
  }, [])

  // 关闭详情弹窗
  const closeDetail = useCallback(() => {
    setDetailVisible(false)
    setSelectedRecord(null)
  }, [])

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    showDetail,
  }), [showDetail])

  return (
    <Modal
      title={
        <div style={{
          fontSize: '18px',
          fontWeight: 600,
          color: '#262626',
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: '12px',
          marginBottom: '16px'
        }}>
          任务详情
        </div>
      }
      open={detailVisible}
      onCancel={closeDetail}
      footer={null}
      width={DETAIL_MODAL_WIDTH}
      styles={{
        body: { padding: '20px 24px' },
        header: { padding: '16px 24px 0' }
      }}
    >
      {selectedRecord && (
        <div>
          <Descriptions
            column={2}
            style={{ marginBottom: 20 }}
            labelStyle={{
              fontWeight: 600,
              color: '#595959',
              fontSize: '14px'
            }}
            contentStyle={{
              color: '#262626',
              fontSize: '14px'
            }}
          >
            <Descriptions.Item label="任务ID">
              <code style={{
                background: '#f6f8fa',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '13px',
                fontWeight: 500
              }}>
                {selectedRecord.id}
              </code>
            </Descriptions.Item>
            <Descriptions.Item label="物料名称">
              <span style={{ fontWeight: 500, color: '#1890ff' }}>
                {selectedRecord.materialDetail.title}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="组件名称">
              <code style={{
                background: '#e6f7ff',
                color: '#52c41a',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '13px',
                fontWeight: 500,
              }}>
                {selectedRecord.materialDetail.name}
              </code>
            </Descriptions.Item>
            <Descriptions.Item label="命名空间">
              <code style={{
                background: '#f6f8fa',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '13px'
              }}>
                {selectedRecord.materialDetail.namespace}
              </code>
            </Descriptions.Item>
            <Descriptions.Item label="版本">
              <code style={{
                background: '#f6f8fa',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '13px'
              }}>
                {selectedRecord.materialVersion}
              </code>
            </Descriptions.Item>
            <Descriptions.Item label="任务状态">
              {getStagingTag(selectedRecord.staging)}
            </Descriptions.Item>
            <Descriptions.Item label="记录状态">
              {getStateTag(selectedRecord.state)}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间" span={2}>
              <span style={{ color: '#595959', fontSize: '13px' }}>
                {formatTime(selectedRecord.createTime)}
              </span>
            </Descriptions.Item>
            {selectedRecord.resultId && (
              <Descriptions.Item label="结果ID">
                <code style={{
                  background: '#f6f8fa',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '13px',
                  fontWeight: 500
                }}>
                  {selectedRecord.resultId}
                </code>
              </Descriptions.Item>
            )}
            {selectedRecord.failedReason && (
              <Descriptions.Item label="失败原因" span={2}>
                <Text
                  type="danger"
                  style={{
                    background: '#fff2f0',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    border: '1px solid #ffccc7',
                    display: 'block',
                    lineHeight: '1.5'
                  }}
                >
                  {selectedRecord.failedReason}
                </Text>
              </Descriptions.Item>
            )}
          </Descriptions>

          {(selectedRecord.rawResult || selectedRecord.rawConversation) && (
            <Card
              title={
                <span style={{ fontSize: '16px', fontWeight: 600, color: '#262626' }}>
                  执行结果
                </span>
              }
              size="small"
              style={{
                borderRadius: '8px',
                border: '1px solid #e8f4fd',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)'
              }}
              styles={{ body: { padding: '16px' } }}
            >
              <Space size={12}>
                {selectedRecord.rawResult && (
                  <Button
                    type="primary"
                    icon={<FileTextOutlined />}
                    onClick={() => window.open(selectedRecord.rawResult!, '_blank')}
                    style={{
                      borderRadius: '6px',
                      fontWeight: 500,
                      boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)'
                    }}
                  >
                    查看生成结果
                  </Button>
                )}
                {selectedRecord.rawConversation && (
                  <Button
                    icon={<FileTextOutlined />}
                    onClick={() => window.open(selectedRecord.rawConversation!, '_blank')}
                    style={{
                      borderRadius: '6px',
                      fontWeight: 500
                    }}
                  >
                    查看执行过程
                  </Button>
                )}
              </Space>
            </Card>
          )}
        </div>
      )}
    </Modal>
  )
})

JobDetailSection.displayName = 'JobDetailSection'

export default JobDetailSection
