import React, { useRef } from 'react'
import JobListSection from './JobListSection'
import JobDetailSection from './JobDetailSection'
import JobTriggerSection from './JobTriggerSection'
import JobHistorySection from './JobHistorySection'
import type {
  MaterialSmartDescriptionJob,
  JobListSectionRef,
  JobDetailSectionRef,
  JobTriggerSectionRef,
  JobHistorySectionRef
} from './shared/types'
import styles from './MaterialSmartDescriptionJobList.module.less'

const MaterialSmartDescriptionJobList: React.FC = () => {
  // 各功能模块的引用
  const listRef = useRef<JobListSectionRef>(null)
  const detailRef = useRef<JobDetailSectionRef>(null)
  const triggerRef = useRef<JobTriggerSectionRef>(null)
  const historyRef = useRef<JobHistorySectionRef>(null)

  // 处理查看详情
  const handleViewDetail = (record: MaterialSmartDescriptionJob) => {
    detailRef.current?.showDetail(record)
  }

  // 处理查看历史
  const handleViewHistory = (record: MaterialSmartDescriptionJob) => {
    historyRef.current?.showHistory(record)
  }

  // 处理任务触发成功
  const handleTriggerSuccess = () => {
    listRef.current?.refresh()
    listRef.current?.refreshStats()
  }

  // 历史记录中查看详情
  const handleHistoryViewDetail = (record: MaterialSmartDescriptionJob) => {
    detailRef.current?.showDetail(record)
  }

  return (
    <div className={styles.container}>
      {/* 列表区域 */}
      <JobListSection
        ref={listRef}
        onViewDetail={handleViewDetail}
        onViewHistory={handleViewHistory}
      />

      {/* 详情区域 */}
      <JobDetailSection ref={detailRef} />

      {/* 触发任务区域 */}
      <JobTriggerSection
        ref={triggerRef}
        onSuccess={handleTriggerSuccess}
      />

      {/* 历史记录区域 */}
      <JobHistorySection
        ref={historyRef}
        onViewDetail={handleHistoryViewDetail}
      />
    </div>
  )
}

export default MaterialSmartDescriptionJobList
