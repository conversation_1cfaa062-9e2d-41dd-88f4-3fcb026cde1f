import { useState, useCallback, useEffect, forwardRef, useImperativeHandle } from 'react'
import {
  Modal,
  Table,
  Button,
  Space,
  Typography,
  Pagination,
  Empty,
  Divider,
} from 'antd'
import { EyeOutlined, FileTextOutlined } from '@ant-design/icons'
import { materialSmartDescriptionJobApi } from '../../../services/api'
import { getStateTag } from '@/constants/common'
import { formatTime, getStagingTag } from './shared/utils'
import { HISTORY_MODAL_WIDTH } from './shared/constants'
import type {
  MaterialSmartDescriptionJob,
  MaterialInfo,
  JobHistorySectionRef,
  JobHistorySectionProps
} from './shared/types'

const { Text, Title } = Typography

const JobHistorySection = forwardRef<JobHistorySectionRef, JobHistorySectionProps>(({
  onViewDetail,
}, ref) => {
  // 历史记录状态
  const [historyVisible, setHistoryVisible] = useState(false)
  const [selectedMaterialPubId, setSelectedMaterialPubId] = useState<number | null>(null)
  const [selectedMaterialInfo, setSelectedMaterialInfo] = useState<MaterialInfo | null>(null)

  // 历史数据状态
  const [data, setData] = useState<MaterialSmartDescriptionJob[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)

  // 获取历史记录
  const fetchHistory = useCallback(async (
    materialPubId: number,
    params?: { pageNum?: number, pageSize?: number },
  ) => {
    try {
      setLoading(true)

      const finalParams = {
        pageNum: params?.pageNum ?? currentPage,
        pageSize: params?.pageSize ?? pageSize,
      }

      const response = await materialSmartDescriptionJobApi.getHistoryByMaterialPubId(
        materialPubId,
        finalParams,
      )

      if (response.data.code === 1) {
        setData(response.data.data.list)
        setTotal(response.data.data.total)
        setCurrentPage(response.data.data.pageNum)
        setPageSize(response.data.data.pageSize)
      }
    }
    catch (error) {
      console.error('获取历史记录失败:', error)
      setData([])
      setTotal(0)
    }
    finally {
      setLoading(false)
    }
  }, [currentPage, pageSize])

  // 分页变化处理
  const handlePageChange = useCallback((page: number, size?: number) => {
    const newSize = size || pageSize
    setCurrentPage(page)
    if (size) {
      setPageSize(size)
    }

    if (selectedMaterialPubId) {
      fetchHistory(selectedMaterialPubId, {
        pageNum: page,
        pageSize: newSize,
      })
    }
  }, [pageSize, selectedMaterialPubId, fetchHistory])

  // 重置状态
  const reset = useCallback(() => {
    setData([])
    setLoading(false)
    setTotal(0)
    setCurrentPage(1)
  }, [])

  // 显示历史记录
  const showHistory = useCallback((record: MaterialSmartDescriptionJob) => {
    setSelectedMaterialPubId(record.materialPubId)
    setSelectedMaterialInfo({
      title: record.materialDetail.title,
      name: record.materialDetail.name,
      namespace: record.materialDetail.namespace,
      version: record.materialVersion,
    })
    setHistoryVisible(true)
  }, [])

  // 关闭历史记录
  const closeHistory = useCallback(() => {
    setHistoryVisible(false)
    setSelectedMaterialPubId(null)
    setSelectedMaterialInfo(null)
    reset()
  }, [reset])

  // 加载历史记录
  useEffect(() => {
    if (historyVisible && selectedMaterialPubId) {
      fetchHistory(selectedMaterialPubId)
    }
    else if (!historyVisible) {
      reset()
    }
  }, [historyVisible, selectedMaterialPubId, fetchHistory, reset])

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    showHistory,
  }), [showHistory])

  // 表格列定义
  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: number) => <Text code>{id}</Text>,
    },
    {
      title: '任务状态',
      key: 'status',
      width: 120,
      render: (record: MaterialSmartDescriptionJob) => (
        <div>
          <div style={{ marginBottom: 4 }}>{getStagingTag(record.staging)}</div>
          {record.failedReason && (
            <Text type="danger" style={{ fontSize: 12 }}>
              {record.failedReason}
            </Text>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      render: (createTime: number) => formatTime(createTime),
    },
    {
      title: '记录状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: (state: number) => getStateTag(state),
    },
    {
      title: '结果',
      key: 'result',
      width: 200,
      render: (record: MaterialSmartDescriptionJob) => (
        <Space direction="vertical" size="small">
          {record.resultId && (
            <div>
              <Text type="secondary">结果ID：</Text>
              <Text code>{record.resultId}</Text>
            </div>
          )}
          <Space size="small">
            {record.rawResult && (
              <Button
                type="link"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => window.open(record.rawResult!, '_blank')}
              >
                查看结果
              </Button>
            )}
            {record.rawConversation && (
              <Button
                type="link"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => window.open(record.rawConversation!, '_blank')}
              >
                查看对话
              </Button>
            )}
          </Space>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (record: MaterialSmartDescriptionJob) => (
        <Button
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => onViewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ]

  return (
    <Modal
      title={(
        <div style={{
          borderBottom: '1px solid #f0f0f0',
          paddingBottom: '12px',
          marginBottom: '16px'
        }}>
          <Title level={4} style={{
            margin: 0,
            fontSize: '18px',
            fontWeight: 600,
            color: '#262626'
          }}>
            历史执行记录
          </Title>
          {selectedMaterialInfo && (
            <div style={{
              marginTop: 12,
              padding: '8px 12px',
              background: '#f6f8fa',
              borderRadius: '6px',
              border: '1px solid #e1e4e8'
            }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>物料：</Text>
              <Text strong style={{ fontSize: '13px', color: '#1890ff' }}>
                {selectedMaterialInfo.title}
              </Text>
              <Text style={{ fontSize: '12px', color: '#8c8c8c', margin: '0 4px' }}>
                ({selectedMaterialInfo.name})
              </Text>
              <Divider type="vertical" style={{ margin: '0 8px' }} />
              <Text type="secondary" style={{ fontSize: '12px' }}>命名空间：</Text>
              <Text code style={{
                fontSize: '12px',
                background: '#fff',
                padding: '2px 4px',
                borderRadius: '3px'
              }}>
                {selectedMaterialInfo.namespace}
              </Text>
              <Divider type="vertical" style={{ margin: '0 8px' }} />
              <Text type="secondary" style={{ fontSize: '12px' }}>版本：</Text>
              <Text code style={{
                fontSize: '12px',
                background: '#fff',
                padding: '2px 4px',
                borderRadius: '3px'
              }}>
                {selectedMaterialInfo.version}
              </Text>
            </div>
          )}
        </div>
      )}
      open={historyVisible}
      onCancel={closeHistory}
      footer={null}
      width={HISTORY_MODAL_WIDTH}
      style={{ top: 20 }}
      styles={{
        body: { padding: '20px 24px' },
        header: { padding: '16px 24px 0' }
      }}
    >
      {data.length === 0 && !loading
        ? (
          <div style={{
            textAlign: 'center',
            padding: '60px 40px',
            background: '#fafbfc',
            borderRadius: '8px',
            border: '1px dashed #d0d7de'
          }}>
            <Empty
              description={
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  暂无历史记录
                </Text>
              }
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        )
        : (
          <>
            <div style={{
              border: '1px solid #e8f4fd',
              borderRadius: '8px',
              overflow: 'hidden'
            }}>
              <Table
                columns={columns}
                dataSource={data}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ x: 1000 }}
                size="small"
                style={{
                  background: '#fff'
                }}
              />
            </div>

            {total > 0 && (
              <div style={{
                marginTop: 20,
                textAlign: 'right',
                paddingTop: '16px',
                borderTop: '1px solid #f0f0f0'
              }}>
                <Pagination
                  current={currentPage}
                  pageSize={pageSize}
                  total={total}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
                  onChange={handlePageChange}
                  onShowSizeChange={handlePageChange}

                />
              </div>
            )}
          </>
        )}
    </Modal>
  )
})

JobHistorySection.displayName = 'JobHistorySection'

export default JobHistorySection
