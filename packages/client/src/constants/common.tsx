import { Tag } from 'antd'

// 物料智能描述相关常量
export const RECORD_STATE = {
  DELETED: -1,
  PENDING: 0,
  NORMAL: 1,
} as const

export const STATE_LABELS = {
  [RECORD_STATE.DELETED]: { text: '已删除', color: 'red' },
  [RECORD_STATE.PENDING]: { text: '待上线', color: 'orange' },
  [RECORD_STATE.NORMAL]: { text: '正常', color: 'green' },
} as const

export const getStateTag = (state: number): React.ReactElement => {
  const config = STATE_LABELS[state as keyof typeof STATE_LABELS]
  if (!config) {
    return <Tag>未知</Tag>
  }
  return <Tag color={config.color}>{config.text}</Tag>
}
