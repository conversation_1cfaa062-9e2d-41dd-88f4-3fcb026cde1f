<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
  y="0px" viewBox="0 0 74.3 93.2" style="enable-background:new 0 0 74.3 93.2;" xml:space="preserve">
  <style type="text/css">
    .st0 {
      fill: url(#SVGID_1_);
    }

    .st1 {
      fill: url(#SVGID_2_);
    }

    .st2 {
      fill: url(#SVGID_3_);
    }
  </style>
  <g id="SvgjsG1755"
    transform="matrix(0.9299113006123226,0,0,0.9299113006123226,-19.715500625615242,-3.8584413712241377)">

    <linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="-769.1113" y1="625.0883" x2="-768.1113"
      y2="625.0883" gradientTransform="matrix(74.3096 0 0 -55.8655 57173.5664 34995.1719)">
      <stop offset="0" style="stop-color:#6D7CFF" />
      <stop offset="1" style="stop-color:#FF51FF" />
    </linearGradient>
    <polygon class="st0" points="21.2,62.5 21.2,86.2 25.2,74.1 28.3,87 34.5,72.7 37.3,94.3 44.6,66.9 52.2,88.8 56.3,61.5 
		60.2,104.4 67.2,77.4 69.8,82 73,77.9 76.2,98 79.2,78.1 82,88.2 83.8,77.9 89,104.4 93.4,70.8 101.1,94.3 101.1,62.5 61.2,44.3 	
		" />

    <linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="-769.1113" y1="617.4029" x2="-768.1113"
      y2="617.4029" gradientTransform="matrix(74.3096 0 0 -30.8113 57173.5664 19064.3438)">
      <stop offset="0" style="stop-color:#6D7CFF" />
      <stop offset="1" style="stop-color:#FF51FF" />
    </linearGradient>
    <polygon class="st1" points="61.2,24.8 21.2,43 21.2,57.9 61.2,39.7 101.1,57.9 101.1,43 	" />

    <linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="-769.1113" y1="617.4029" x2="-768.1113"
      y2="617.4029" gradientTransform="matrix(74.3096 0 0 -30.8113 57173.5664 19043.7051)">
      <stop offset="0" style="stop-color:#6D7CFF" />
      <stop offset="1" style="stop-color:#FF51FF" />
    </linearGradient>
    <polygon class="st2" points="61.2,4.1 21.2,22.4 21.2,37.3 61.2,19.1 101.1,37.3 101.1,22.4 	" />
  </g>
</svg>