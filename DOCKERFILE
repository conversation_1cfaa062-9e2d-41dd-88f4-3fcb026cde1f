FROM registry.corp.kuaishou.com/cloud-ci/product/kwaishop-fangzhou-global-material-calc-service:1.0.66-master-e1ee1dd3-43387096

# Copy project files
COPY . /home/<USER>/kuaishou-nodejs/project

# Set working directory
WORKDIR /home/<USER>/kuaishou-nodejs/project

# Make start.sh executable
RUN chmod +x start.sh

# Set the user to run the container as
USER root

# Define the command to run when the container starts
CMD ["kcsize", "api"]
