# 批量 Trigger 接口实现

## 任务概述

为 material-smart-description-job 模块创建批量触发接口，支持一次性为多个物料触发智能描述生成任务，并发执行且单个失败不阻塞整体流程。

## 执行过程

### 1. 需求分析
- 原有接口：`POST /trigger` 单个物料触发
- 新增需求：`POST /trigger/batch` 批量物料触发
- 核心要求：并发执行、错误隔离、详细结果反馈

### 2. 架构设计
- **Controller 层**：新增批量接口端点
- **Service 层**：实现批量处理逻辑
- **类型定义**：完善 TypeScript 类型系统
- **并发控制**：限制并发数为 5，分块处理

### 3. 实现细节
- 复用现有 `trigger` 方法，避免重复代码
- 采用 Promise.all 实现批内并发
- 分块策略避免过多并发请求
- 详细的错误处理和日志记录

## 代码变动

### 新增文件
1. `docs/api/batch-trigger.md` - API 文档
2. `scripts/test-batch-trigger.js` - 测试脚本
3. `docs/examples/batch-trigger-example.ts` - 使用示例

### 修改文件
1. `material-smart-description-job.controller.ts`
   - 新增 `triggerBatch` 接口方法
   
2. `material-smart-description-job.service.ts`
   - 新增 `triggerBatch` 服务方法
   - 实现并发控制和错误处理逻辑
   
3. `material-smart-description-job.d.ts`
   - 新增批量接口相关类型定义
   - `BatchTriggerRequest`、`BatchTriggerResponse`、`BatchTriggerItemResult`

## 接口规范

### 请求格式
```typescript
POST /material-smart-description-job/trigger/batch
{
  materials: MaterialIdentifierParams[]
  options: {
    invokeLangBridgeBizKey: string
    invokeLangBridgeOperator: string
  }
}
```

### 响应格式
```typescript
{
  total: number
  succeeded: number
  failed: number
  results: Array<{
    material: MaterialIdentifierParams
    success: boolean
    jobId?: number
    error?: string
  }>
}
```

## 关键决策点

### 1. 并发控制策略
- **决策**：限制并发数为 5，分块处理
- **理由**：平衡性能和系统稳定性，避免过多并发请求导致系统压力

### 2. 错误处理策略
- **决策**：单个失败不影响整体流程
- **理由**：提高批量处理的可靠性，允许部分成功的场景

### 3. 接口设计
- **决策**：复用现有 trigger 方法
- **理由**：保持代码一致性，减少重复逻辑

## 遇到的问题与解决方案

### 1. TypeScript 类型定义
- **问题**：批量接口需要完善的类型定义
- **解决**：在 .d.ts 文件中新增相关类型，提高类型安全性

### 2. 并发控制实现
- **问题**：如何控制并发数量避免系统压力
- **解决**：采用分块 + Promise.all 的策略，批内并发、批间串行

### 3. 错误处理粒度
- **问题**：如何处理单个物料失败的情况
- **解决**：使用 try-catch 包装每个物料的处理，记录详细错误信息

## 测试验证

### 测试脚本
- 创建了 `scripts/test-batch-trigger.js` 用于功能验证
- 支持环境变量配置服务器地址和超时时间

### 测试用例
1. **基本功能测试**：正常批量触发
2. **错误处理测试**：包含无效物料的批量请求
3. **并发控制测试**：大批量请求的分块处理

## 优化建议

### 1. 性能优化
- 考虑增加批量大小限制（如最多 50 个物料）
- 可配置的并发数设置
- 添加请求去重逻辑

### 2. 监控增强
- 添加批量处理的性能指标
- 增加失败率监控和告警
- 记录批量处理的详细统计信息

### 3. 功能扩展
- 支持批量查询处理状态
- 添加批量取消功能
- 实现批量处理的进度回调

## 后续计划

### 短期计划
1. 部署到测试环境进行集成测试
2. 编写单元测试用例
3. 完善错误码和错误信息

### 中期计划
1. 添加批量处理的监控面板
2. 优化并发控制策略
3. 支持更多批量操作场景

### 长期计划
1. 考虑引入消息队列处理大批量任务
2. 实现分布式批量处理
3. 提供批量处理的可视化界面

## 经验总结

### 技术要点
1. **并发控制**：合理的并发策略是批量处理的关键
2. **错误隔离**：单点失败不应影响整体流程
3. **类型安全**：完善的类型定义提高代码质量
4. **可观测性**：详细的日志和统计信息便于问题排查

### 最佳实践
1. 复用现有逻辑，避免重复开发
2. 分层设计，保持代码结构清晰
3. 完善的文档和示例，便于使用和维护
4. 充分的测试验证，确保功能稳定性

### 注意事项
1. 批量处理可能耗时较长，需要合理设置超时时间
2. 大批量请求可能对系统造成压力，需要监控和限制
3. 错误信息要足够详细，便于问题定位和处理
