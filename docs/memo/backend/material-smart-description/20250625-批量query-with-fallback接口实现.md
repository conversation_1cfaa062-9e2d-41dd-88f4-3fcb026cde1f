# 批量 Query-with-Fallback 接口实现

## 任务概述

为 material-smart-description 模块创建批量查询智能描述接口，支持版本回退功能。入参变成数组，响应除了数组化之外，还要展示对应的 item 的参数。

## 执行过程

### 1. 需求分析
- **原有接口**: `POST /query-with-fallback` 单个物料查询
- **新增需求**: `POST /query-with-fallback/batch` 批量物料查询
- **核心要求**: 
  - 入参数组化：接受 MaterialIdentifierParams[] 
  - 响应包含原始参数：每个结果项都包含对应的 input 参数
  - 继承版本回退机制
  - 并发执行、错误隔离

### 2. 架构设计
- **Controller 层**: 新增批量查询端点
- **Service 层**: 实现批量处理逻辑，复用现有单个查询方法
- **类型定义**: 扩展 TypeScript 类型系统
- **并发控制**: 限制并发数为 5，分块处理

### 3. 实现细节
- 复用现有 `getDescriptionByMaterialIdentifierWithFallback` 方法
- 采用 Promise.all 实现批内并发
- 分块策略避免过多并发请求
- 详细的错误处理和日志记录
- 每个结果项包含原始输入参数

## 代码变动

### 新增文件
1. `scripts/test-batch-query-with-fallback.js` - 测试脚本
2. `docs/api/batch-query-with-fallback.md` - API 文档
3. `docs/examples/batch-query-with-fallback-example.ts` - 使用示例

### 修改文件
1. `material-smart-description.d.ts`
   - 新增 `BatchQueryWithFallbackRequest` 类型
   - 新增 `BatchQueryWithFallbackItemResult` 类型
   - 新增 `BatchQueryWithFallbackResponse` 类型

2. `material-smart-description.service.ts`
   - 新增 `batchGetDescriptionByMaterialIdentifierWithFallback` 方法
   - 实现并发控制和错误处理逻辑

3. `material-smart-description.controller.ts`
   - 新增 `batchGetDescriptionByMaterialIdentifierWithFallback` 接口方法

## 接口规范

### 请求格式
```typescript
POST /material-smart-description/query-with-fallback/batch
{
  materials: MaterialIdentifierParams[]
}

interface MaterialIdentifierParams {
  materialId?: number
  namespace?: string
  materialVersionName?: string
}
```

### 响应格式
```typescript
{
  total: number
  succeeded: number
  failed: number
  results: Array<{
    input: MaterialIdentifierParams  // 原始输入参数
    success: boolean
    data?: ZhiDaNeededMaterialDescriptionWithVersion
    error?: string
  }>
}
```

### 核心特性
- **入参数组化**: 接受物料标识参数数组
- **响应包含原始参数**: 每个结果项都包含对应的 input 参数
- **版本回退支持**: 继承原有的 fallback 机制
- **并发优化**: 5个并发请求，分块处理
- **错误隔离**: 单个失败不阻塞整体流程

## 测试结果

### 测试脚本功能
- 多种测试场景（成功、失败、不存在的物料）
- 详细的结果验证和格式检查
- 性能统计和错误分析
- 响应格式完整性验证

### 预期测试结果
- 成功物料返回完整的智能描述信息
- 失败物料返回错误信息但不影响其他物料
- 每个结果项都包含原始的输入参数
- 版本回退机制正常工作

## 关键决策点

### 1. 类型设计
- 复用现有的 `Service.BatchOperationItemResult` 通用类型
- 扩展 `MaterialIdentifierParams` 作为输入参数
- 保持与现有批量接口的一致性

### 2. 并发控制
- 选择 5 个并发数，平衡性能和资源消耗
- 分块处理策略，避免单次请求过大
- 使用 Promise.all 确保批内并发执行

### 3. 错误处理
- 单个查询失败不影响整体流程
- 详细的错误信息记录
- 统计成功/失败数量便于监控

### 4. 代码复用
- 复用现有的单个查询方法，避免重复实现
- 保持版本回退逻辑的一致性
- 利用现有的验证和日志机制

## 遇到的问题与解决方案

### 1. 类型定义复杂性
**问题**: 批量接口的类型定义较为复杂，需要正确继承通用类型
**解决方案**: 使用 TypeScript 的泛型继承，确保类型安全和一致性

### 2. 响应格式设计
**问题**: 需要在响应中包含原始输入参数，同时保持与现有批量接口的一致性
**解决方案**: 使用 `Service.BatchOperationItemResult` 通用类型，其中 `input` 字段包含原始参数

### 3. 并发控制
**问题**: 需要平衡并发性能和系统资源消耗
**解决方案**: 采用分块策略，每块最多 5 个并发请求，逐块处理

## 优化建议

### 1. 性能优化
- 考虑添加缓存机制，避免重复查询相同物料
- 监控并发数的实际效果，根据系统负载调整
- 考虑实现查询去重，避免同一批次中的重复物料

### 2. 功能增强
- 添加查询进度回调机制
- 支持部分结果的流式返回
- 实现更智能的重试机制

### 3. 监控和日志
- 添加详细的性能指标监控
- 记录批量查询的使用模式
- 监控失败率和常见错误类型

## 后续计划

### 1. 测试验证
- 运行测试脚本验证接口功能
- 进行压力测试验证并发性能
- 测试各种边界情况和错误场景

### 2. 文档完善
- 根据实际测试结果更新 API 文档
- 添加更多使用场景的示例
- 完善错误码和故障排除指南

### 3. 生产部署
- 配置监控和告警
- 设置合适的限流和熔断机制
- 准备回滚方案

## 经验总结

### 1. 设计原则
- **一致性**: 保持与现有批量接口的设计一致性
- **复用性**: 最大化复用现有代码，避免重复实现
- **可扩展性**: 设计支持未来功能扩展的架构

### 2. 实现技巧
- **类型安全**: 充分利用 TypeScript 的类型系统
- **错误隔离**: 确保单个失败不影响整体流程
- **性能优化**: 合理的并发控制和资源管理

### 3. 质量保证
- **完整测试**: 覆盖各种场景的测试用例
- **详细文档**: 提供清晰的 API 文档和使用示例
- **监控机制**: 建立完善的监控和日志体系

这次实现成功地创建了一个功能完整、性能优化、易于使用的批量查询接口，为后续的批量操作需求奠定了良好的基础。
